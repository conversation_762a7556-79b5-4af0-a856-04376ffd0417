<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.good.mapper.GoodStoreListMapper">

    <sql id="goodInfo">
        gsl.id AS id,
        gsl.main_picture AS mainPicture,
        gsl.share_picture as sharePicture,
        0 AS isPlatform,
        gsl.good_name AS goodName,
        gsl.sys_user_id as  sysUserId,
        gsl.share_commission_rate as shareCommissionRate,
        ( SELECT price FROM good_store_specification WHERE good_store_list_id = gsl.id AND del_flag = '0' ORDER BY price ASC LIMIT 1 ) AS smallPrice,
        ( SELECT vip_price FROM good_store_specification WHERE good_store_list_id = gsl.id AND del_flag = '0' ORDER BY price ASC LIMIT 1 ) AS smallVipPrice,
        ( SELECT SUM( sales_volume ) FROM good_store_specification WHERE good_store_list_id = gsl.id AND del_flag = '0' ) AS salesVolume,
        ( SELECT SUM( repertory ) FROM good_store_specification WHERE good_store_list_id = gsl.id AND del_flag = '0' ) AS stock,
        gsl.market_price AS marketPrice,
        gsl.total_performance as salesPerformance,
        ROUND(( SELECT price FROM good_store_specification WHERE good_store_list_id = gsl.id AND del_flag = '0' ORDER BY price ASC LIMIT 1 ) * gsl.share_commission_rate / 100, 2) AS minShareCommission,
        ( SELECT CONCAT( store_name, "(", sub_store_name, ")" ) FROM `store_manage` WHERE sys_user_id = gsl.sys_user_id AND del_flag = "0" LIMIT 1 ) AS storeName,
<!--        (-->
<!--        SELECT-->
<!--            COUNT( 1 )-->
<!--        FROM-->
<!--            marketing_discount_good mg-->
<!--            LEFT JOIN marketing_discount ms ON mg.marketing_discount_id = ms.id-->
<!--        WHERE-->
<!--            ms.STATUS = 1-->
<!--            AND ms.del_flag = 0-->
<!--            AND ms.total &gt; ms.released_quantity-->
<!--            AND mg.good_id = gsl.id-->
<!--            AND mg.is_platform = '0'-->
<!--        ) AS discount,-->
        gsl.is_specification AS isSpecification,
        gsl.specification AS specification,
        gsl.specifications AS specifications
    </sql>


    <sql id="goodWhere">
        gsl.del_flag = '0'
        AND gsl.frame_status = '1'
        AND gsl.audit_status = '2'
        AND gsl.`status` = '1'
    </sql>


    <select id="selectGood" parameterType="map" resultType="map">
        SELECT
            gsl.id,
            gsl.market_price AS marketPrice,
            gsl.main_picture AS mainPicture,
            gsl.share_picture as sharePicture,
            CONCAT( gstOne.`name`, '-', gstTwo.`name` ) AS typeName,
            gsl.good_name AS goodName
        FROM
            good_store_list gsl
            LEFT JOIN good_store_type gstTwo ON gsl.`good_store_type_id` = gstTwo.`id`
            LEFT JOIN good_store_type gstOne ON gstTwo.`parent_id` = gstOne.`id`
        WHERE
            gsl.del_flag = '0'
            and gsl.sys_user_id=#{paramMap.sysUserId}
            <if test="paramMap.goodName!=null and paramMap.goodName!=''">
                and gsl.good_name LIKE CONCAT(CONCAT('%',#{paramMap.goodName}),'%')
            </if>
        <if test="paramMap.goodTypeId!=null and paramMap.goodTypeId!=''">
            and gsl.good_store_type_id=#{paramMap.goodTypeId}
        </if>
        ORDER BY
            gsl.update_time DESC
    </select>



<select id="queryPageList" resultType="map" parameterType="map">
    SELECT * FROM (
        SELECT
               gsl.main_picture                                       AS mainPicture,
               gsl.share_picture                                      as sharePicture,
               gsl.id,
               gsl.`status`,
               gsl.good_name                                          AS goodName,
               gt.`parent_id`                                         AS typeTwo,
               gsl.`good_store_type_id`                               AS typeThree,
               gsl.audit_status                                       AS auditStatus,
               (SELECT cost_price
                FROM good_store_specification
                WHERE good_store_list_id = gsl.id
                  AND del_flag = '0'
                ORDER BY cost_price ASC
                LIMIT 1)                                              AS minCostPrice,
               (SELECT cost_price
                FROM good_store_specification
                WHERE good_store_list_id = gsl.id
                  AND del_flag = '0'
                ORDER BY cost_price DESC
                LIMIT 1)                                              AS maxCostPrice,
               (SELECT vip_price
                FROM good_store_specification
                WHERE good_store_list_id = gsl.id
                  AND del_flag = '0'
                ORDER BY vip_price ASC
                LIMIT 1)                                              AS minVipPrice,
               (SELECT vip_price
                FROM good_store_specification
                WHERE good_store_list_id = gsl.id
                  AND del_flag = '0'
                ORDER BY vip_price DESC
                LIMIT 1)                                              AS maxVipPrice,
               (SELECT price
                FROM good_store_specification
                WHERE good_store_list_id = gsl.id AND del_flag = '0'
                ORDER BY price ASC
                LIMIT 1)                                              AS minPrice,
               (SELECT price
                FROM good_store_specification
                WHERE good_store_list_id = gsl.id AND del_flag = '0'
                ORDER BY price DESC
                LIMIT 1)                                              AS maxPrice,
               gsl.create_by                                          AS createBy,
               DATE_FORMAT(gsl.`create_time`, '%Y-%m-%d %H:%i:%s')    AS createTime,
               (SELECT IFNULL(SUM(sales_volume), 0)
                FROM good_store_specification
                WHERE good_store_list_id = gsl.id AND del_flag = '0') AS salesVolume,
               (SELECT CONCAT(gtTwo.NAME, '-', gtOne.NAME)
                FROM good_store_type gtOne
                         LEFT JOIN good_store_type gtTwo ON gtTwo.id = gtOne.parent_id
                WHERE gtOne.id = gsl.`good_store_type_id`)            AS goodTypeName,
               gsl.is_specification                                   AS isSpecification,
               gsl.status_explain                                     AS statusExplain,
               gsl.good_no                                            AS goodNo,
               gsl.details_goods                                      AS detailsGoods,
               gsl.commitment_customers                               AS commitmentCustomers,
               gsl.specifications,
               gsl.specification,
               gsl.sys_user_id                                        AS sysUserId,
               gsl.store_template_id                                  AS storeTemplateId,
               gsl.market_price                                       AS marketPrice,
               gsl.frame_status                                       AS frameStatus,
               gsl.good_describe                                      AS goodDescribe,
               gsl.distribution                                       as distribution,
               gsl.is_wholesale                                       as isWholesale,
               gsl.is_selected_products                               as isSelectedProducts,
               gsl.product_store_commission_rate                      as productStoreCommissionRate,
               gsl.product_agency_commission_rate                     as productAgencyCommissionRate,
               gsl.share_commission_rate                              as shareCommissionRate,
               gsl.selected_store_commission_rate                     as selectedStoreCommissionRate,
               gsl.product_agency_commission_rate_two                 as productAgencyCommissionRateTwo,
               gsl.product_agency_commission_rate_myself              as productAgencyCommissionRateMyself,
               gsl.repertory                                          as repertory,
               gsl.update_time                                        as updateTime,
               gsl.sort                                               as sort,
               gsl.create_time                                        as create_time
        FROM (SELECT * FROM `good_store_list` ${ew.customSqlSegment}) gsl
                 LEFT JOIN `good_store_type` gt ON gsl.`good_store_type_id` = gt.`id`
        WHERE gsl.del_flag = '0'
          and (SELECT count(1)
               from store_manage
               where sys_user_id = gsl.sys_user_id
                 and del_flag = '0'
                 and `status` = '1'
                 AND pay_status IN ('1', '2')) > 0
        <if test="paramMap.level == 1">
            and gt.`parent_id` = #{paramMap.typeId}
        </if>
        <if test="paramMap.level == 2">
            and gsl.`good_store_type_id` = #{paramMap.typeId}
        </if>
    ) AS result
    <if test="paramMap.pattern == null or paramMap.pattern == 0">
        ORDER BY result.salesVolume desc, result.updateTime desc
    </if>
    <if test="paramMap.pattern == 1">
        ORDER BY result.salesVolume desc
    </if>
    <if test="paramMap.pattern == 2">
        ORDER BY result.updateTime desc
    </if>
    <if test="paramMap.pattern == 3">
        ORDER BY result.minPrice desc
    </if>
    <if test="paramMap.pattern == 4">
        ORDER BY result.minPrice asc
    </if>
    <!-- <if test="paramMap.pattern == null">
        ORDER BY result.sort desc, result.create_time DESC
    </if> -->
</select>




    <select id="getGoodStoreListById" resultType="org.jeecg.modules.good.entity.GoodStoreList">
        select * from  good_store_list  where 1=1
        <if test="id!=null and id!=''">
          and  id =#{id}
        </if>

    </select>

    <update id="updateDelFalg">
        UPDATE good_store_list SET del_flag=#{delFlag}WHERE id=#{id}
    </update>
    <select id="getGoodListDto" resultType="org.jeecg.modules.good.dto.GoodStoreListDto">
        /*select * from  good_store_list  where del_flag = 0*/
        SELECT gstm.*,
               sm.store_name     AS storeName,
               sm.sub_store_name AS subStoreName,
               sm.good_audit     AS goodAudit
        FROM (SELECT gsl.*,
                     gsts.goodStoreTypeIdTwo   AS goodStoreTypeIdTwo,
                     gsts.goodStoreTypeIdOne   AS goodStoreTypeIdOne,
                     gsts.goodStoreTypeTwoName AS goodStoreTypeTwoName,
                     gsts.goodStoreTypeOneName AS goodStoreTypeOneName,
                    ( SELECT cost_price FROM good_store_specification WHERE good_store_list_id = gsl.id AND del_flag = '0' ORDER BY cost_price ASC LIMIT 1 ) AS minCostPrice,
                    ( SELECT cost_price FROM good_store_specification WHERE good_store_list_id = gsl.id AND del_flag = '0' ORDER BY cost_price DESC LIMIT 1 ) AS maxCostPrice,
                    ( SELECT vip_price FROM good_store_specification WHERE good_store_list_id = gsl.id AND del_flag = '0' ORDER BY vip_price ASC LIMIT 1 ) AS minVipPrice,
                    ( SELECT vip_price FROM good_store_specification WHERE good_store_list_id = gsl.id AND del_flag = '0' ORDER BY vip_price DESC LIMIT 1 ) AS maxVipPrice,
                    ( SELECT price FROM good_store_specification WHERE good_store_list_id = gsl.id AND del_flag = '0' ORDER BY price ASC LIMIT 1 ) AS minPrice,
                    ( SELECT price FROM good_store_specification WHERE good_store_list_id = gsl.id AND del_flag = '0' ORDER BY price DESC LIMIT 1 ) AS maxPrice,
                     CONCAT_WS(
                             '-',
                             gsts.goodStoreTypeOneName,
                             gsts.goodStoreTypeTwoName
                     )                         AS goodStoreTypeNames
              FROM good_store_list gsl
                       LEFT JOIN
                   (SELECT gst.*,
                           gst.id      AS goodStoreTypeIdTwo,
                           gst.name    AS goodStoreTypeTwoName,
                           gstOne.id   AS goodStoreTypeIdOne,
                           gstOne.name AS goodStoreTypeOneName
                    FROM good_store_type gst
                             LEFT JOIN good_store_type gstOne
                                       ON gst.parent_id = gstOne.id) gsts
                   ON gsl.good_store_type_id = gsts.id where gsl.del_flag = '0'
                    <if test="goodListVo.isExposureGood != null and goodListVo.isExposureGood != ''">
                        and gsl.is_exposure_good = #{goodListVo.isExposureGood}
                    </if>
        ) gstm
                 LEFT JOIN `store_manage` sm ON gstm.sys_user_id = sm.sys_user_id
        WHERE gstm.del_flag = 0
        <!--不显示草稿箱信息-->
        <if test="notauditStatus != null and notauditStatus != ''">
            AND gstm.audit_status != 0
        </if>

        <if test="goodListVo != null and goodListVo.goodStoreTypeId != null and goodListVo.goodStoreTypeId != ''">
            AND gstm.goodStoreTypeIdTwo = #{goodListVo.goodStoreTypeId}
        </if>
        <if test="goodListVo != null and goodListVo.goodStoreTypeIdTwo != null and goodListVo.goodStoreTypeIdTwo != ''">
            AND gstm.goodStoreTypeIdTwo = #{goodListVo.goodStoreTypeIdTwo}
        </if>
        <if test="goodListVo != null and goodListVo.goodStoreTypeIdOne != null and goodListVo.goodStoreTypeIdOne != ''">
            AND gstm.goodStoreTypeIdOne = #{goodListVo.goodStoreTypeIdOne}
        </if>
        <if test="goodListVo != null and goodListVo.goodName != null and goodListVo.goodName != ''">
            AND gstm.good_name LIKE CONCAT(CONCAT('%', #{goodListVo.goodName}), '%')
        </if>
        <if test="goodListVo != null and goodListVo.storeName != null and goodListVo.storeName != ''">
            AND sm.store_name LIKE CONCAT(CONCAT('%', #{goodListVo.storeName}), '%')
        </if>
        <if test="goodListVo != null and goodListVo.subStoreName != null and goodListVo.subStoreName != ''">
            AND sm.sub_store_name LIKE CONCAT(CONCAT('%', #{goodListVo.subStoreName}), '%')
        </if>
        <if test="goodListVo != null and goodListVo.goodNo != null and goodListVo.goodNo != ''">
            AND gstm.good_no LIKE CONCAT(CONCAT('%', #{goodListVo.goodNo}), '%')
        </if>
        <if test="goodListVo != null and goodListVo.sysUserId != null and goodListVo.sysUserId != ''">
            AND gstm.sys_user_id = #{goodListVo.sysUserId}
        </if>
        <if test="goodListVo != null and goodListVo.frameStatus != null and goodListVo.frameStatus != ''">
            AND gstm.frame_status = #{goodListVo.frameStatus}
        </if>
        <if test="goodListVo != null and goodListVo.auditStatus != null and goodListVo.auditStatus != ''">
            AND gstm.audit_status = #{goodListVo.auditStatus}
        </if>
        <if test="goodListVo != null and goodListVo.status != null and goodListVo.status != ''">
            AND gstm.status = #{goodListVo.status}
        </if>
<!--        <if test="goodListVo != null and goodListVo.activitiesType != null and goodListVo.activitiesType != ''">-->
<!--            AND gstm.activities_type = #{goodListVo.activitiesType}-->
<!--        </if>-->
        <if test="goodListVo != null and goodListVo.createTime_begin != null and goodListVo.createTime_begin != ''">
            and gstm.create_time <![CDATA[>=]]> concat(#{goodListVo.createTime_begin}, ' 00:00:00')
        </if>
        <if test="goodListVo != null and goodListVo.createTime_end != null and goodListVo.createTime_end != ''">
            AND gstm.create_time <![CDATA[<=]]> concat(#{goodListVo.createTime_end}, ' 23:59:59')
        </if>
        <if test="goodListVo != null and goodListVo.strings != null and goodListVo.strings.size > 0">
            and  gstm.id in
            <foreach collection="goodListVo.strings" item="string" separator="," open="(" close=")">
                #{string}
            </foreach>
        </if>
        ORDER BY  gstm.create_time DESC
<!--        <choose>-->
<!--            <when test="page.orders != null and page.orders.size() > 0">-->
<!--                <foreach collection="page.orders" item="item" separator=",">-->
<!--                    ${item.column} ${item.asc ? 'ASC' : 'DESC'}-->
<!--                </foreach>-->
<!--            </when>-->
<!--            <otherwise>-->
<!--                gstm.create_time DESC-->
<!--            </otherwise>-->
<!--        </choose>-->
    </select>

    <select id="findStoreGoodList" resultType="org.jeecg.modules.good.dto.GoodStoreDiscountDTO">
        SELECT
        gsl.`id`,
        gsl.`main_picture`,
        gsl.share_picture,
        gsl.`good_name`,
        CONCAT( gstOne.`name`, '-', gstTwo.`name` ) AS typeName,
        gsl.`market_price`,
        CONCAT(
        ( SELECT price FROM good_store_specification WHERE good_store_list_id = gsl.id AND del_flag = '0' ORDER BY price ASC LIMIT 1 ),
        '-',(
        SELECT
        price
        FROM
        good_store_specification
        WHERE
        good_store_list_id = gsl.id
        AND del_flag = '0'
        ORDER BY
        price DESC
        LIMIT 1
        )) AS price,
        CONCAT((
        SELECT
        cost_price
        FROM
        good_store_specification
        WHERE
        good_store_list_id = gsl.id
        AND del_flag = '0'
        ORDER BY
        cost_price ASC
        LIMIT 1
        ),
        '-',(
        SELECT
        cost_price
        FROM
        good_store_specification
        WHERE
        good_store_list_id = gsl.id
        AND del_flag = '0'
        ORDER BY
        cost_price DESC
        LIMIT 1
        )
        ) AS cost_price,
        CONCAT(
        ( SELECT vip_price FROM good_store_specification WHERE good_store_list_id = gsl.id AND del_flag = '0' ORDER BY price ASC LIMIT 1 ),
        '-',(
        SELECT
        price
        FROM
        good_store_specification
        WHERE
        good_store_list_id = gsl.id
        AND del_flag = '0'
        ORDER BY
        price DESC
        LIMIT 1
        )) AS price,
        CONCAT(
        ( SELECT vip_price FROM good_store_specification WHERE good_store_list_id = gsl.id AND del_flag = '0' ORDER BY price ASC LIMIT 1 ),
        '-',(
        SELECT
        price
        FROM
        good_store_specification
        WHERE
        good_store_list_id = gsl.id
        AND del_flag = '0'
        ORDER BY
        price DESC
        LIMIT 1
        )) AS `vip_price`,
        ( SELECT SUM( repertory ) FROM good_store_specification WHERE good_store_list_id = gsl.id AND del_flag = '0' ) AS repertory,
        gsl.create_time
        FROM
        good_store_list gsl
        LEFT JOIN good_store_type gstTwo ON gsl.`good_store_type_id` = gstTwo.`id`
        LEFT JOIN good_store_type gstOne ON gstTwo.`parent_id` = gstOne.`id`
        WHERE
        gsl.`del_flag` = '0'
        AND gsl.`audit_status` = 2
        AND gsl.`frame_status` = 1
        AND gsl.`status` = 1
              <if test="goodListVo!=null and goodListVo.sysUserId !=null and goodListVo.sysUserId !=''">
              AND gsl.sys_user_id = #{goodListVo.sysUserId}
              </if>
                <if test="goodListVo != null and goodListVo.goodName!=null and goodListVo.goodName!=''">
                    AND gsl.good_name LIKE CONCAT(CONCAT('%',#{goodListVo.goodName}),'%')
                </if>
            <if test="goodListVo.isSelectedProducts != null and goodListVo.isSelectedProducts != ''">
                and gsl.is_selected_products = #{goodListVo.isSelectedProducts}
            </if>
            <if test="goodListVo.isWholesale != null and goodListVo.isWholesale != ''">
                AND gsl.is_wholesale = #{goodListVo.isWholesale}
            </if>
            ORDER BY gsl.`create_time` DESC
    </select>



    <select id="getGoodListDtoDelFlag" resultType="org.jeecg.modules.good.dto.GoodStoreListDto">
        SELECT
            gsl.*,
            gsts.goodStoreTypeIdTwo AS goodStoreTypeIdTwo,
            gsts.goodStoreTypeIdOne AS goodStoreTypeIdOne,
            gsts.goodStoreTypeTwoName AS goodStoreTypeTwoName,
            gsts.goodStoreTypeOneName AS goodStoreTypeOneName,
            CONCAT_WS(
            '-',
            gsts.goodStoreTypeOneName,
            gsts.goodStoreTypeTwoName
            ) AS goodStoreTypeNames
            FROM
            good_store_list gsl
            LEFT JOIN
            (SELECT
            gst.*,
            gst.id AS goodStoreTypeIdTwo,
            gst.name AS goodStoreTypeTwoName,
            gstOne.id AS goodStoreTypeIdOne,
            gstOne.name AS goodStoreTypeOneName
            FROM
            good_store_type gst
            LEFT JOIN good_store_type gstOne
            ON gst.parent_id = gstOne.id) gsts
            ON gsl.good_store_type_id = gsts.id
            WHERE gsl.del_flag = 1
        <if test="goodListVo != null and goodListVo.goodStoreTypeId!=null and goodListVo.goodStoreTypeId!=''">
            AND goodStoreTypeIdTwo =#{goodListVo.goodStoreTypeId}
        </if>
        <if test="goodListVo != null and goodListVo.goodStoreTypeIdTwo!=null and goodListVo.goodStoreTypeIdTwo!=''">
            AND goodStoreTypeIdTwo =#{goodListVo.goodStoreTypeIdTwo}
        </if>
        <if test="goodListVo != null and goodListVo.goodStoreTypeIdOne!=null and goodListVo.goodStoreTypeIdOne!=''">
            AND goodStoreTypeIdOne =#{goodListVo.goodStoreTypeIdOne}
        </if>
        <if test="goodListVo != null and goodListVo.goodName!=null and goodListVo.goodName!=''">
            AND gsl.good_name LIKE CONCAT(CONCAT('%',#{goodListVo.goodName}), '%')
        </if>
        <if test="goodListVo != null and goodListVo.goodNo!=null and goodListVo.goodNo!=''">
            AND gsl.good_no LIKE CONCAT(CONCAT('%',#{goodListVo.goodNo}), '%')
        </if>
        <if test="goodListVo != null and goodListVo.sysUserId!=null and goodListVo.sysUserId!=''">
            AND gsl.sys_user_id =#{goodListVo.sysUserId}
        </if>
        <if test="goodListVo != null and goodListVo.frameStatus!=null and goodListVo.frameStatus!=''">
            AND gsl.frame_status =#{goodListVo.frameStatus}
        </if>
        <if test="goodListVo != null and goodListVo.auditStatus!=null and goodListVo.auditStatus!=''">
            AND gsl.audit_status =#{goodListVo.auditStatus}
        </if>
        <if test="goodListVo != null and goodListVo.status!=null and goodListVo.status!=''">
            AND gsl.status =#{goodListVo.status}
        </if>
        <if test="goodListVo != null and goodListVo.activitiesType!=null and goodListVo.activitiesType!=''">
            AND gsl.activities_type =#{goodListVo.activitiesType}
        </if>
        <if test="goodListVo != null and goodListVo.createTime_begin != null and goodListVo.createTime_begin != ''">
            and gsl.create_time <![CDATA[>=]]> concat(#{goodListVo.createTime_begin},' 00:00:00')
        </if>
        <if test="goodListVo != null and goodListVo.createTime_end != null and goodListVo.createTime_end != ''">
            AND gsl.create_time <![CDATA[<=]]> concat( #{goodListVo.createTime_end},' 23:59:59')
        </if>
        <if test="goodListVo.isSelectedProducts != null and goodListVo.isSelectedProducts != ''">
            and gsl.is_selected_products = #{goodListVo.isSelectedProducts}
        </if>
        <if test="goodListVo.isWholesale != null and goodListVo.isWholesale != ''">
            AND gsl.is_wholesale = #{goodListVo.isWholesale}
        </if>
         ORDER BY gsl.create_time DESC


    </select>

    <select id="findGoodListByGoodType" resultType="map" parameterType="map">
        SELECT
        <include refid="goodInfo"></include>,
        IFNULL(( SELECT quantity FROM member_shopping_cart WHERE member_list_id = #{paramMap.memberId} AND is_view = '1' AND del_flag = '0' AND good_store_list_id = gsl.id  ORDER BY create_time desc  limit 1 ),'0') AS carCount,
        IFNULL(( SELECT id FROM member_shopping_cart WHERE member_list_id = #{paramMap.memberId} AND is_view = '1' AND del_flag = '0' AND good_store_list_id = gsl.id  ORDER BY create_time desc  limit 1 ),'0') AS memberShoppingCartId
        FROM
        good_store_list gsl
        LEFT JOIN store_manage sm ON gsl.sys_user_id = sm.sys_user_id
        WHERE
     <include refid="goodWhere"></include>
        AND sm.status = '1' AND sm.del_flag = '0'
        <if test="paramMap.goodTypeId!=null">and good_store_type_id=#{paramMap.goodTypeId}</if>
        <if test="paramMap.pattern==0">order by (SELECT IFNULL(SUM(sales_volume), 0) FROM good_store_specification WHERE good_store_list_id = gsl.id AND del_flag = '0') desc, gsl.update_time desc</if>
        <if test="paramMap.pattern==1">order by (SELECT IFNULL(SUM(sales_volume), 0) FROM good_store_specification WHERE good_store_list_id = gsl.id AND del_flag = '0') desc</if>
        <if test="paramMap.pattern==2">order by gsl.update_time desc</if>
        <if test="paramMap.pattern==3">order by (SELECT IFNULL(MIN(price), 0) FROM good_store_specification WHERE good_store_list_id = gsl.id AND del_flag = '0') desc</if>
        <if test="paramMap.pattern==4">order by (SELECT IFNULL(MIN(price), 0) FROM good_store_specification WHERE good_store_list_id = gsl.id AND del_flag = '0') asc</if>
        <if test="paramMap.pattern==5">order by gsl.share_commission_rate desc </if>
    </select>

    <select id="searchGoodList" resultType="map" parameterType="map">

        SELECT
       <include refid="goodInfo"></include>,
        IFNULL(( SELECT quantity FROM member_shopping_cart WHERE member_list_id = #{paramMap.memberId} AND is_view = '1' AND del_flag = '0' AND good_store_list_id = gsl.id  ORDER BY create_time desc  limit 1 ),'0') AS carCount,
        IFNULL(( SELECT id FROM member_shopping_cart WHERE member_list_id = #{paramMap.memberId} AND is_view = '1' AND del_flag = '0' AND good_store_list_id = gsl.id  ORDER BY create_time desc  limit 1 ),'0') AS memberShoppingCartId
        FROM
        good_store_list gsl
        LEFT JOIN store_manage sm ON gsl.sys_user_id = sm.sys_user_id
        WHERE
      <include refid="goodWhere"></include>
        AND sm.status = '1' AND sm.del_flag = '0'
        <if test="paramMap.search!=null">and good_name like concat('%',#{paramMap.search},'%') or good_describe like concat('%',#{paramMap.search},'%')</if>
        <if test="paramMap.sysUserId!=null">and gsl.sys_user_id = #{paramMap.sysUserId}</if>
        <if test="paramMap.storeType!=null">and gsl.store_type = #{paramMap.storeType}</if>
        <if test="paramMap.pattern==0">order by (SELECT IFNULL(SUM(sales_volume), 0) FROM good_store_specification WHERE good_store_list_id = gsl.id AND del_flag = '0') desc, gsl.update_time desc</if>
        <if test="paramMap.pattern==1">order by (SELECT IFNULL(SUM(sales_volume), 0) FROM good_store_specification WHERE good_store_list_id = gsl.id AND del_flag = '0') desc</if>
        <if test="paramMap.pattern==2">order by gsl.update_time desc</if>
        <if test="paramMap.pattern==3">order by (SELECT IFNULL(MIN(price), 0) FROM good_store_specification WHERE good_store_list_id = gsl.id AND del_flag = '0') desc</if>
        <if test="paramMap.pattern==4">order by (SELECT IFNULL(MIN(price), 0) FROM good_store_specification WHERE good_store_list_id = gsl.id AND del_flag = '0') asc</if>
        <if test="paramMap.pattern==5">order by gsl.share_commission_rate desc </if>
    </select>

    <select id="findGoodListBySysUserId" resultType="map">
        SELECT
         <include refid="goodInfo"></include>
        FROM
          good_store_list gsl
          LEFT JOIN store_manage sm ON gsl.sys_user_id = sm.sys_user_id
        WHERE
        <include refid="goodWhere"></include>
          AND sm.status = '1' AND sm.del_flag = '0'
          AND gsl.sys_user_id = #{sysUserId} ORDER BY salesVolume DESC,smallPrice,gsl.update_time
    </select>
    <select id="findGoodListBySysUserIds" resultType="map">
        SELECT
         <include refid="goodInfo"></include>
        FROM
          good_store_list gsl
        WHERE
        <include refid="goodWhere"></include>
        <if test="sysUserIds != null and sysUserIds.size() != 0">
            AND sys_user_id in
            <foreach collection="sysUserIds" separator="," open="(" close=")" item="item" index="index">
                #{item}
            </foreach>
        </if>


        ORDER BY salesVolume DESC,smallPrice,update_time
    </select>

    <!-- 曝光商品列表查询 - 基于现有goodInfo和goodWhere SQL片段 -->
    <select id="getExposureGoodsList" resultType="map" parameterType="map">
        SELECT
        <include refid="goodInfo"></include>,
        IFNULL((SELECT quantity FROM member_shopping_cart
                WHERE member_list_id = #{paramMap.memberId}
                AND is_view = '1' AND del_flag = '0'
                AND good_store_list_id = gsl.id
                ORDER BY create_time desc LIMIT 1), '0') AS carCount,
        IFNULL((SELECT id FROM member_shopping_cart
                WHERE member_list_id = #{paramMap.memberId}
                AND is_view = '1' AND del_flag = '0'
                AND good_store_list_id = gsl.id
                ORDER BY create_time desc LIMIT 1), '0') AS memberShoppingCartId,
        gsl.is_exposure_good AS isExposureGood,
        gsl.exposure_performance AS exposurePerformance,
        gsl.exposure_sort_value AS exposureSortValue
        FROM
        good_store_list gsl
        LEFT JOIN store_manage sm ON gsl.sys_user_id = sm.sys_user_id
        WHERE
        <include refid="goodWhere"></include>
        AND sm.status = '1' AND sm.del_flag = '0'
        AND gsl.is_exposure_good = '1'
        AND gsl.repertory > 0
        ORDER BY gsl.share_commission_rate DESC, IFNULL(gsl.exposure_sort_value, 999) ASC, gsl.update_time DESC
    </select>

    <!-- 曝光商品业绩排行榜查询 -->
    <select id="getExposureGoodsPerformanceRanking" resultType="map" parameterType="map">
        SELECT
        gsl.id,
        gsl.good_name AS goodName,
        gsl.main_picture AS mainPicture,
        gsl.share_picture AS sharePicture,
        gsl.small_price AS smallPrice,
        gsl.small_vip_price AS smallVipPrice,
        gsl.share_commission_rate AS shareCommissionRate,
        gsl.exposure_performance AS exposurePerformance,
        gsl.repertory,
        gsl.create_time AS createTime,
        gsl.update_time AS updateTime,
        sm.store_name AS storeName,
        sm.sub_store_name AS subStoreName,
        (SELECT IFNULL(SUM(sales_volume), 0)
         FROM good_store_specification
         WHERE good_store_list_id = gsl.id AND del_flag = '0') AS salesVolume
        FROM
        good_store_list gsl
        LEFT JOIN store_manage sm ON gsl.sys_user_id = sm.sys_user_id
        WHERE
        gsl.del_flag = '0'
        AND gsl.is_exposure_good = '1'
        AND sm.status = '1' AND sm.del_flag = '0'
        AND gsl.exposure_performance > 0
        ORDER BY gsl.exposure_performance DESC, gsl.update_time DESC
    </select>

    <select id="findGoodListByGoodId" resultType="map">
            SELECT
                id AS id,
                main_picture AS mainPicture,
                share_picture as sharePicture,
                0 AS isPlatform,
                good_name AS goodName,
                ( SELECT price FROM good_store_specification WHERE good_store_list_id = gsl.id AND del_flag = '0' ORDER BY price ASC LIMIT 1 ) AS smallPrice,
                good_describe AS goodDescribe,
                market_price AS marketPrice,
                ( SELECT vip_price FROM good_store_specification WHERE good_store_list_id = gsl.id AND del_flag = '0' ORDER BY price ASC LIMIT 1 ) AS smallVipPrice,
                specification AS specification,
                good_video AS goodVideo,
                details_goods AS detailsGoods,
                ( SELECT SUM( repertory ) FROM good_store_specification WHERE good_store_list_id = gsl.id AND del_flag = '0' ) AS repertory,
                frame_status AS frameStatus,
                share_commission_rate AS shareCommissionRate,
                ROUND(( SELECT price FROM good_store_specification WHERE good_store_list_id = gsl.id AND del_flag = '0' ORDER BY price ASC LIMIT 1 ) * share_commission_rate / 100, 2) AS minShareCommission,
                good_store_type_id AS goodTypeId,
                gsl.specifications,
                gsl.sys_user_id as sysUserId,
                gsl.distribution as distribution,
                gsl.share_commission_rate as shareCommissionRate
        FROM
            good_store_list gsl
        WHERE
            del_flag = 0
          AND STATUS = 1
          AND id = #{goodId}
    </select>

    <select id="getEverydayGoodStoreTypeId" resultType="map">
        SELECT
        good_store_type_id as id,
        COUNT(*) AS number
        FROM
        good_store_list
        WHERE del_flag = 0
        AND frame_status = '1'
        AND STATUS = '1'
        <if test="createTime != null and createTime!=null and createTime!=''">
            AND create_time  LIKE CONCAT(CONCAT('%',#{createTime}), '%')
        </if>
        <if test="sysUserId!=null and sysUserId!=''">
            AND sys_user_id =#{sysUserId}
        </if>
        GROUP BY good_store_type_id
        ORDER BY number DESC
        <if test="limit!=null and limit!=''">
            LIMIT #{limit}
        </if>
    </select>


    <select id="getEveryWeekPreferential" resultType="map" parameterType="map">

        SELECT id AS id,main_picture AS mainPicture,share_picture as sharePicture,0 AS isPlatform,good_name AS goodName,small_price AS  smallPrice,small_vip_price AS smallVipPrice,activities_type AS activitiesType,activity_price AS activityPrice,
        (SELECT CONCAT(store_name,"(",sub_store_name,")") AS storeName  FROM `store_manage` WHERE sys_user_id=g.sys_user_id  AND del_flag="0" LIMIT 1) AS storeName,
        create_time as createTime,
        (SELECT COUNT(1) FROM
        marketing_discount_good mg
        LEFT JOIN marketing_discount ms
        ON mg.marketing_discount_id = ms.id
        WHERE ms.status = 1
        AND ms.del_flag = 0
        AND ms.total &gt;ms.released_quantity AND mg.good_id = g.id AND mg.is_platform='0') AS discount
        FROM  good_store_list g  WHERE del_flag = '0' AND STATUS=1 AND frame_status=1 and audit_status = 2
        <if test="paramMap != null and paramMap.smallPriceMin != null and paramMap.smallPriceMin != ''">
            and small_price <![CDATA[ >= ]]> #{paramMap.smallPriceMin}
        </if>
        <if test="paramMap != null and paramMap.smallPriceMax != null and paramMap.smallPriceMax != ''">
            AND small_price <![CDATA[ <= ]]> #{paramMap.smallPriceMax}
        </if>
        <if test="paramMap != null and paramMap.dateBegin != null and paramMap.dateBegin != ''">
            and create_time <![CDATA[>=]]> concat(#{paramMap.dateBegin},' 00:00:00')
        </if>
        <if test="paramMap != null and paramMap.dateEnd != null and paramMap.dateEnd != ''">
            AND create_time <![CDATA[<=]]> concat( #{paramMap.dateEnd},' 23:59:59')
        </if>
        UNION
        SELECT id AS id,main_picture AS mainPicture,1 AS isPlatform,good_name AS goodName,small_price AS  smallPrice,small_vip_price AS smallVipPrice,activities_type AS activitiesType,activity_price AS activityPrice,''  AS storeName,create_time as createTime,
        (SELECT COUNT(1) FROM
        marketing_discount_good mg
        LEFT JOIN marketing_discount ms
        ON mg.marketing_discount_id = ms.id
        WHERE ms.status = 1
        AND ms.del_flag = 0
        AND ms.total &gt;ms.released_quantity AND mg.good_id = gl.id AND mg.is_platform='1') AS discount
         FROM  good_list gl WHERE del_flag = '0' AND STATUS=1 AND frame_status=1 AND good_form='0' and audit_status = 2
        <if test="paramMap != null and paramMap.smallPriceMin != null and paramMap.smallPriceMin != ''">
            and small_price <![CDATA[ >= ]]> #{paramMap.smallPriceMin}
        </if>
        <if test="paramMap != null and paramMap.smallPriceMax != null and paramMap.smallPriceMax != ''">
            AND small_price <![CDATA[ <= ]]> #{paramMap.smallPriceMax}
        </if>
        <if test="paramMap != null and paramMap.dateBegin != null and paramMap.dateBegin != ''">
            and create_time <![CDATA[>=]]> concat(#{paramMap.dateBegin},' 00:00:00')
        </if>
        <if test="paramMap != null and paramMap.dateEnd != null and paramMap.dateEnd != ''">
            AND create_time <![CDATA[<=]]> concat( #{paramMap.dateEnd},' 23:59:59')
        </if>
        ORDER BY mainPicture
    </select>


    <!--查询规格商品里有0库存的商品和目前总库存-->
    <select id="getGoodStoreListIdAndRepertory" resultType="map">
        SELECT gls.id AS id ,SUM(gss.repertory) AS repertory FROM `good_store_list` gls LEFT JOIN good_store_specification gss ON gls.id  = gss.`good_store_list_id` WHERE
        gls.id IN (
        SELECT
        gs.good_store_list_id
        FROM
        `good_store_specification` gs
        LEFT JOIN `good_store_list` gl
        ON gl.id = gs.good_store_list_id
        WHERE gs.del_flag = 0
        AND gs.repertory &lt;= 0
        AND gl.`del_flag` = '0'
        AND gl.frame_status = 1
        GROUP BY gs.`good_store_list_id`
        )
        AND gss.`del_flag` = '0'
        GROUP BY gss.good_store_list_id
    </select>

    <select id="getGoodStoreListdelFlag" resultType="java.lang.Integer">
        select COUNT(0) from  good_store_list  where del_flag =1
        <if test="sysUserId!=null and sysUserId!=''">
            and sys_user_id= #{sysUserId}
        </if>
    </select>
    <select id="getGoodStoreListMaps" resultType="map">
        SELECT
          id,
          0 AS isPlatform,
          good_name AS goodName,
          main_picture AS mainPicture,
          share_picture as sharePicture,
          repertory,
          audit_status as auditStatus,
          price
        FROM
          `good_store_list`
        WHERE 1 = 1
        <if test="paramMap.containsKey('sysUserId')">
         AND   sys_user_id =#{paramMap.sysUserId}
        </if>
        <if test="paramMap.containsKey('goodStoreTypeId')">
            AND   good_store_type_id =#{paramMap.goodStoreTypeId}
        </if>
        <if test="paramMap.containsKey('search')">and good_name like concat('%',#{paramMap.search},'%') or good_describe like concat('%',#{paramMap.search},'%')</if>

        <!--店铺商品总数-->
        <if test="paramMap.goodStatus==0">AND del_flag = 0 AND audit_status != 0</if>
        <!--店铺 在售中（已上架）商品数-->
        <if test="paramMap.goodStatus==1">AND del_flag = 0 AND audit_status = 2 AND status =1 AND frame_status = 1 AND repertory > 0</if>
        <!--店铺 已下架 商品数-->
        <if test="paramMap.goodStatus==2">AND del_flag = 0 AND audit_status = 2 AND status =1 AND frame_status = 0 </if>
        <!--店铺 已售罄 商品数-->
        <if test="paramMap.goodStatus==3">AND del_flag = 0 AND audit_status = 2 AND status =1 AND frame_status = 1 AND (repertory = 0 OR repertory IS NULL)</if>
        <!--店铺 发布中（审核中）商品数-->
        <if test="paramMap.goodStatus==4">AND del_flag = 0 AND audit_status = 1 AND status =1 </if>
        <!--店铺 已驳回 商品数-->
        <if test="paramMap.goodStatus==5">AND del_flag = 0 AND audit_status = 3 AND status =1 </if>
        <!--店铺 草稿箱 商品数-->
        <if test="paramMap.goodStatus==6">AND del_flag = 0 AND audit_status = 0 </if>
        <!--回收站商品-->
        <if test="paramMap.goodStatus==7">AND del_flag = 1 </if>
        order by create_time
    </select>
    <select id="searchGoodListStore" resultType="map" >
        SELECT * FROM (
            SELECT g.id                         AS id,
                   g.main_picture               AS mainPicture,
                   0                            AS isPlatform,
                   g.good_name                  AS goodName,
                   g.share_picture              as sharePicture,
                   (SELECT CONCAT(store_name, "(", sub_store_name, ")")
                    FROM `store_manage`
                    WHERE sys_user_id = g.sys_user_id
                      AND del_flag = "0"
                    LIMIT 1)                    AS storeName,
                   (SELECT COUNT(1)
                    FROM marketing_discount_good mg
                             LEFT JOIN marketing_discount ms
                                       ON mg.marketing_discount_id = ms.id
                    WHERE ms.status = 1
                      AND ms.del_flag = 0
                      AND ms.total &gt; ms.released_quantity
                      AND mg.good_id = g.id
                      AND mg.is_platform = '0') AS discount,
                   mdgs.COUNT                   AS marketingDiscountGoodCount,
                   (SELECT IFNULL(SUM(sales_volume), 0)
                    FROM good_store_specification
                    WHERE good_store_list_id = g.id AND del_flag = '0') AS salesVolume,
                   (SELECT IFNULL(MIN(price), 0)
                    FROM good_store_specification
                    WHERE good_store_list_id = g.id AND del_flag = '0') AS minPrice,
                   g.update_time                AS updateTime
            FROM good_store_list g
                     LEFT JOIN
                 (SELECT gst.*,
                         gst.id      AS goodStoreTypeIdTwo,
                         gst.name    AS goodStoreTypeTwoName,
                         gstOne.id   AS goodStoreTypeIdOne,
                         gstOne.name AS goodStoreTypeOneName
                  FROM good_store_type gst
                           LEFT JOIN good_store_type gstOne
                                     ON gst.parent_id = gstOne.id) gsts ON g.good_store_type_id = gsts.id
                     LEFT JOIN (SELECT mdg.good_id, COUNT(0) AS COUNT
                                FROM marketing_discount_good mdg
                                WHERE mdg.`del_flag` = '0'
                                  AND mdg.is_platform = 0
                                GROUP BY mdg.good_id) mdgs
                               ON mdgs.good_id = g.id
            WHERE g.del_flag = '0' AND g.status = 1 AND g.frame_status = 1 AND g.audit_status = 2

            <if test="searchTermsVO != null and searchTermsVO.search != null and searchTermsVO.search != ''">
                and g.good_name like concat('%', #{searchTermsVO.search}, '%')
                   or g.good_describe like concat('%', #{searchTermsVO.search}, '%')
            </if>
            <if test="searchTermsVO != null and searchTermsVO.goodTypeId != null and searchTermsVO.goodTypeId != ''">
                and gsts.goodStoreTypeIdOne = #{searchTermsVO.goodTypeId}
            </if>
            <if test="searchTermsVO != null and searchTermsVO.goodTypeIdTwo != null and searchTermsVO.goodTypeIdTwo != ''">
                and gsts.goodStoreTypeIdTwo = #{searchTermsVO.goodTypeIdTwo}
            </if>
            <if test="searchTermsVO != null and searchTermsVO.marketingDiscountGoodCount != null and searchTermsVO.marketingDiscountGoodCount != ''">
                and mdgs.COUNT <![CDATA[>=]]> #{searchTermsVO.marketingDiscountGoodCount}
            </if>
            <if test="searchTermsVO != null and searchTermsVO.sysUserId != null and searchTermsVO.sysUserId != ''">
                and g.sys_user_id = #{searchTermsVO.sysUserId}
            </if>
        ) AS result
        <if test="searchTermsVO.pattern == 0">
            order by result.salesVolume desc, result.updateTime desc
        </if>
        <if test="searchTermsVO.pattern == 1">
            order by result.salesVolume desc
        </if>
        <if test="searchTermsVO.pattern == 2">
            order by result.updateTime desc
        </if>
        <if test="searchTermsVO.pattern == 3">
            order by result.minPrice desc
        </if>
        <if test="searchTermsVO.pattern == 4">
            order by result.minPrice asc
        </if>
    </select>

</mapper>
