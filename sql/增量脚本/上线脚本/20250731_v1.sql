-- 打榜者功能数据库结构扩展脚本
-- 执行时间：2025-01-20
-- 说明：新增打榜者相关字段和表结构，确保向后兼容性

-- =====================================================
-- 1. 会员表(member_list)扩展 - 新增打榜者相关字段
-- =====================================================

-- 新增打榜者身份字段
ALTER TABLE member_list ADD COLUMN is_ranker VARCHAR(1) DEFAULT '0' COMMENT '是否打榜者身份：0=否，1=是';

-- 新增打榜者身份过期时间字段
ALTER TABLE member_list ADD COLUMN ranker_expire_time DATETIME COMMENT '打榜者身份过期时间';

-- 新增打榜者业绩字段
ALTER TABLE member_list ADD COLUMN ranker_performance DECIMAL(12,2) DEFAULT 0.00 COMMENT '打榜者业绩';

-- 新增打榜者申请次数字段（用于统计）
ALTER TABLE member_list ADD COLUMN ranker_apply_count INT DEFAULT 0 COMMENT '打榜者申请次数';

-- 添加索引优化查询性能
CREATE INDEX idx_member_list_ranker ON member_list(is_ranker, ranker_expire_time);
CREATE INDEX idx_member_list_ranker_performance ON member_list(is_ranker, ranker_performance);

-- 为现有会员初始化打榜者相关字段
UPDATE member_list SET 
  is_ranker = '0',
  ranker_performance = 0.00,
  ranker_apply_count = 0
WHERE is_ranker IS NULL;

-- 2. 创建符合规范的打榜者申请表
CREATE TABLE `ranker_application` (
  -- === 标准字段 ===
  `id` varchar(32) NOT NULL COMMENT '主键ID',
  `del_flag` varchar(1) DEFAULT '0' COMMENT '删除状态 0=未删除 1=已删除',
  `create_by` varchar(32) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT (now()) not null COMMENT '创建时间',
  `update_by` varchar(32) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  
  -- === 业务字段 ===
  `member_id` varchar(32) NOT NULL COMMENT '申请人会员ID（关联member_list表）',
  `recommender_phone` varchar(20) DEFAULT NULL COMMENT '推荐人手机号',
  `recommender_name` varchar(50) DEFAULT NULL COMMENT '推荐人姓名',
  `recommender_id` varchar(32) DEFAULT NULL COMMENT '推荐人会员ID（关联member_list表）',
  `application_reason` text DEFAULT NULL COMMENT '申请理由',
  `payment_amount` decimal(12,2) DEFAULT 0.00 COMMENT '支付金额',
  `payment_status` varchar(1) DEFAULT '0' COMMENT '支付状态：0=未支付；1=已支付；2=支付失败。字典：ranker_payment_status',
  `application_status` varchar(1) DEFAULT '0' COMMENT '申请状态：0=待审核；1=审核通过；2=审核拒绝；3=已取消。字典：ranker_application_status',
  `audit_time` datetime DEFAULT NULL COMMENT '审核时间',
  `audit_user` varchar(32) DEFAULT NULL COMMENT '审核人员ID',
  `audit_remark` varchar(500) DEFAULT NULL COMMENT '审核备注',
  `effective_time` datetime DEFAULT NULL COMMENT '生效时间',
  `expire_time` datetime DEFAULT NULL COMMENT '过期时间',
  `application_type` varchar(1) COLLATE utf8mb4_unicode_ci DEFAULT '0' COMMENT '申请类型：0=审核申请，1=付费申请。字典：ranker_application_type',
  
  -- === 主键和索引 ===
  PRIMARY KEY (`id`),
  INDEX `idx_member_id` (`member_id`),
  INDEX `idx_recommender_id` (`recommender_id`),
  INDEX `idx_application_status` (`application_status`),
  INDEX `idx_payment_status` (`payment_status`),
  INDEX `idx_create_time` (`create_time`),
  INDEX `idx_audit_time` (`audit_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='打榜者申请表';

-- 3. 创建支付状态字典
SET @dict_id_payment = REPLACE(UUID(), '-', '');

INSERT INTO `sys_dict` (`id`, `dict_code`, `dict_name`, `description`, `del_flag`, `create_by`, `create_time`, `update_by`, `update_time`) 
VALUES (@dict_id_payment, 'ranker_payment_status', '打榜者支付状态', '打榜者申请支付状态字典', '0', 'admin', NOW(), 'admin', NOW());

-- 支付状态字典项
SET @item_id_payment_1 = REPLACE(UUID(), '-', '');
INSERT INTO `sys_dict_item` (`id`, `dict_id`, `item_text`, `item_value`, `item_color`, `description`, `sort_order`, `status`, `create_by`, `create_time`, `update_by`, `update_time`) 
VALUES (@item_id_payment_1, @dict_id_payment, '未支付', '0', 'orange', '未支付状态', 1, 1, 'admin', NOW(), 'admin', NOW());

SET @item_id_payment_2 = REPLACE(UUID(), '-', '');
INSERT INTO `sys_dict_item` (`id`, `dict_id`, `item_text`, `item_value`, `item_color`, `description`, `sort_order`, `status`, `create_by`, `create_time`, `update_by`, `update_time`) 
VALUES (@item_id_payment_2, @dict_id_payment, '已支付', '1', 'green', '已支付状态', 2, 1, 'admin', NOW(), 'admin', NOW());

SET @item_id_payment_3 = REPLACE(UUID(), '-', '');
INSERT INTO `sys_dict_item` (`id`, `dict_id`, `item_text`, `item_value`, `item_color`, `description`, `sort_order`, `status`, `create_by`, `create_time`, `update_by`, `update_time`) 
VALUES (@item_id_payment_3, @dict_id_payment, '支付失败', '2', 'red', '支付失败状态', 3, 1, 'admin', NOW(), 'admin', NOW());

-- 4. 创建申请状态字典
SET @dict_id_application = REPLACE(UUID(), '-', '');

INSERT INTO `sys_dict` (`id`, `dict_code`, `dict_name`, `description`, `del_flag`, `create_by`, `create_time`, `update_by`, `update_time`) 
VALUES (@dict_id_application, 'ranker_application_status', '打榜者申请状态', '打榜者申请状态字典', '0', 'admin', NOW(), 'admin', NOW());

-- 申请状态字典项
SET @item_id_app_1 = REPLACE(UUID(), '-', '');
INSERT INTO `sys_dict_item` (`id`, `dict_id`, `item_text`, `item_value`, `item_color`, `description`, `sort_order`, `status`, `create_by`, `create_time`, `update_by`, `update_time`) 
VALUES (@item_id_app_1, @dict_id_application, '待审核', '0', 'blue', '待审核状态', 1, 1, 'admin', NOW(), 'admin', NOW());

SET @item_id_app_2 = REPLACE(UUID(), '-', '');
INSERT INTO `sys_dict_item` (`id`, `dict_id`, `item_text`, `item_value`, `item_color`, `description`, `sort_order`, `status`, `create_by`, `create_time`, `update_by`, `update_time`) 
VALUES (@item_id_app_2, @dict_id_application, '审核通过', '1', 'green', '审核通过状态', 2, 1, 'admin', NOW(), 'admin', NOW());

SET @item_id_app_3 = REPLACE(UUID(), '-', '');
INSERT INTO `sys_dict_item` (`id`, `dict_id`, `item_text`, `item_value`, `item_color`, `description`, `sort_order`, `status`, `create_by`, `create_time`, `update_by`, `update_time`) 
VALUES (@item_id_app_3, @dict_id_application, '审核拒绝', '2', 'red', '审核拒绝状态', 3, 1, 'admin', NOW(), 'admin', NOW());

SET @item_id_app_4 = REPLACE(UUID(), '-', '');
INSERT INTO `sys_dict_item` (`id`, `dict_id`, `item_text`, `item_value`, `item_color`, `description`, `sort_order`, `status`, `create_by`, `create_time`, `update_by`, `update_time`) 
VALUES (@item_id_app_4, @dict_id_application, '已取消', '3', 'gray', '已取消状态', 4, 1, 'admin', NOW(), 'admin', NOW());

-- 5. 添加系统配置参数
INSERT INTO `sys_dict_item` (`id`, `dict_id`, `item_text`, `item_value`, `description`, `sort_order`, `status`, `create_by`, `create_time`) 
SELECT REPLACE(UUID(), '-', ''), id, '打榜者申请支付金额', '100.00', '打榜者申请需要支付的金额（元）', 999, 1, 'admin', NOW()
FROM `sys_dict` WHERE `dict_code` = 'system_config' LIMIT 1;

INSERT INTO `sys_dict_item` (`id`, `dict_id`, `item_text`, `item_value`, `description`, `sort_order`, `status`, `create_by`, `create_time`) 
SELECT REPLACE(UUID(), '-', ''), id, '打榜者申请是否需要审核', '1', '打榜者申请是否需要人工审核（1/0）', 999, 1, 'admin', NOW()
FROM `sys_dict` WHERE `dict_code` = 'system_config' LIMIT 1;

INSERT INTO sys_dict_item (id, dict_id, item_text, item_value, item_color, description, sort_order, status, create_by, create_time, update_by, update_time) VALUES ('7d0566cf664711f080d80242ac110002', '09e876e1996c62d303cc4cedd49f378f', '打榜者申请支付', '59', null, '打榜者申请时的支付扣款', 590, 1, 'admin', '2025-07-21 15:29:27', 'admin', '2025-07-21 15:29:27');
INSERT INTO sys_dict_item (id, dict_id, item_text, item_value, item_color, description, sort_order, status, create_by, create_time, update_by, update_time) VALUES ('7d056d45664711f080d80242ac110002', '09e876e1996c62d303cc4cedd49f378f', '打榜者申请退款', '60', null, '打榜者申请审核拒绝时的退款', 600, 1, 'admin', '2025-07-21 15:29:27', 'admin', '2025-07-21 15:29:27');


-- =====================================================
-- 6. 菜单权限配置（打榜者管理）
-- =====================================================

-- 生成菜单ID
SET @ranker_main_menu_id = REPLACE(UUID(), '-', '');
SET @ranker_application_menu_id = REPLACE(UUID(), '-', '');

-- 主菜单：打榜者管理
INSERT INTO sys_permission(
    id, parent_id, name, url, component, component_name, redirect, 
    menu_type, perms, perms_type, sort_no, always_show, icon, 
    is_route, is_leaf, keep_alive, hidden, hide_tab, description, 
    status, del_flag, rule_flag, create_by, create_time, update_by, 
    update_time, internal_or_external
) VALUES (
    @ranker_main_menu_id, NULL, '打榜者管理', 
    '/ranker-management', NULL, NULL, '/ranker-management/ranker-application-list', 
    0, NULL, '1', 5.00, 0, 'trophy', 1, 0, 0, 0, 0, '打榜者功能管理模块', 
    '1', 0, 0, 'admin', NOW(), NULL, NULL, 0
);

-- 子菜单：打榜者申请管理
INSERT INTO sys_permission(
    id, parent_id, name, url, component, component_name, redirect, 
    menu_type, perms, perms_type, sort_no, always_show, icon, 
    is_route, is_leaf, keep_alive, hidden, hide_tab, description, 
    status, del_flag, rule_flag, create_by, create_time, update_by, 
    update_time, internal_or_external
) VALUES (
    @ranker_application_menu_id, @ranker_main_menu_id, '打榜者申请管理', 
    '/ranker-management/ranker-application-list', 
    'ranker-management/RankerApplicationList', NULL, NULL, 
    1, 'ranker:application:list', '1', 1.00, 0, 'form', 1, 1, 0, 0, 0, '打榜者申请审核管理', 
    '1', 0, 0, 'admin', NOW(), NULL, NULL, 0
);

-- =====================================================
-- 1. 创建打榜者月度业绩统计表
-- =====================================================

CREATE TABLE `ranker_monthly_performance` (
  -- === 标准字段 ===
  `id` varchar(32) NOT NULL COMMENT '主键ID',
  `create_by` varchar(32) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT (now()) NOT NULL COMMENT '创建时间',
  `update_by` varchar(32) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `del_flag` varchar(1) DEFAULT '0' COMMENT '删除状态 0=未删除 1=已删除',
  
  -- === 业务字段 ===
  `member_id` varchar(32) NOT NULL COMMENT '打榜者会员ID（关联member_list表）',
  `year` int NOT NULL COMMENT '统计年份',
  `month` int NOT NULL COMMENT '统计月份（1-12）',
  `performance_amount` decimal(12,2) DEFAULT 0.00 COMMENT '月度业绩金额',
  `order_count` int DEFAULT 0 COMMENT '月度订单数量',
  `share_count` int DEFAULT 0 COMMENT '月度分享次数',
  `ranking` int DEFAULT 0 COMMENT '月度排名（1为第一名）',
  `last_ranking` int DEFAULT 0 COMMENT '上月排名（用于显示排名变化）',
  `ranking_change` varchar(10) DEFAULT 'new' COMMENT '排名变化：up=上升，down=下降，same=持平，new=新上榜',
  
  -- === 统计字段 ===
  `total_exposure_goods` int DEFAULT 0 COMMENT '曝光商品总数',
  `successful_orders` int DEFAULT 0 COMMENT '成功订单数',
  `conversion_rate` decimal(5,2) DEFAULT 0.00 COMMENT '转化率（%）',
  `avg_order_amount` decimal(10,2) DEFAULT 0.00 COMMENT '平均订单金额',
  
  -- === 会员信息快照（冗余字段，提高查询性能） ===
  `member_nickname` varchar(100) DEFAULT NULL COMMENT '会员昵称（快照）',
  `member_phone` varchar(20) DEFAULT NULL COMMENT '会员手机号（快照）',
  `member_avatar` varchar(500) DEFAULT NULL COMMENT '会员头像（快照）',
  
  -- === 时间字段 ===
  `statistics_time` datetime DEFAULT NULL COMMENT '统计生成时间',
  `period_start` datetime NOT NULL COMMENT '统计周期开始时间',
  `period_end` datetime NOT NULL COMMENT '统计周期结束时间',
  
  PRIMARY KEY (`id`),
  
  -- === 索引设计 ===
  UNIQUE KEY `uk_ranker_monthly_year_month` (`member_id`, `year`, `month`),
  KEY `idx_year_month` (`year`, `month`),
  KEY `idx_performance_ranking` (`year`, `month`, `performance_amount` DESC),
  KEY `idx_member_performance` (`member_id`, `year`, `month`),
  KEY `idx_statistics_time` (`statistics_time`),
  KEY `idx_period` (`period_start`, `period_end`)
  
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='打榜者月度业绩统计表';


-- =============================================
-- 2. 创建申请类型字典配置
-- =============================================

    -- 生成字典主表ID
    SET @dict_id = REPLACE(UUID(), '-', '');
    
    -- 添加字典类型
    INSERT INTO `sys_dict` (
        `id`, `dict_code`, `dict_name`, `description`, `del_flag`,
        `create_by`, `create_time`, `update_by`, `update_time`
    ) VALUES (
        @dict_id, 'ranker_application_type', '打榜者申请类型', '打榜者申请类型字典：审核申请、付费申请', '0',
        'admin', NOW(), 'admin', NOW()
    );
    
    -- 生成字典项ID
    SET @item_id_1 = REPLACE(UUID(), '-', '');
    SET @item_id_2 = REPLACE(UUID(), '-', '');
    
    -- 添加字典项
    INSERT INTO `sys_dict_item` (
        `id`, `dict_id`, `item_text`, `item_value`, `item_color`,
        `description`, `sort_order`, `status`, `create_by`,
        `create_time`, `update_by`, `update_time`
    ) VALUES (
        @item_id_1, @dict_id, '审核申请', '0', 'blue',
        '需要人工审核的申请类型', 1, 1, 'admin',
        NOW(), 'admin', NOW()
    ), (
        @item_id_2, @dict_id, '付费申请', '1', 'green',
        '通过支付获得打榜者身份的申请类型', 2, 1, 'admin',
        NOW(), 'admin', NOW()
    );

ALTER TABLE `good_store_list`
    ADD COLUMN `exposure_sort_value` INT(11) DEFAULT 999 COMMENT '曝光排序值，数值越小优先级越高，默认999';

-- 为已存在的曝光商品设置默认排序值
UPDATE `good_store_list`
SET `exposure_sort_value` = 999
WHERE `exposure_sort_value` IS NULL;