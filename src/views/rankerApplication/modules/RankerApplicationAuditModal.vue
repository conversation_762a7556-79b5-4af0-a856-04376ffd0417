<template>
  <a-modal
    :title="title"
    :width="700"
    :visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleOk"
    @cancel="handleCancel"
    cancelText="关闭">
    
    <a-spin :spinning="confirmLoading">
      <a-form-model ref="form" :model="model" :rules="validatorRules" v-bind="layout">
        


        <!-- 批量审核时显示选中数量 -->
        <div v-if="isBatch" class="batch-audit-info">
          <a-alert
            message="批量审核"
            :description="`您选择了 ${selectedIds.length} 条申请记录进行${auditStatus === '1' ? '通过' : '拒绝'}操作`"
            type="info"
            show-icon
            style="margin-bottom: 16px;"
          />
        </div>

        <!-- 单个审核时显示申请信息 -->
        <div v-if="!isBatch && applicantInfo.id" class="applicant-info">
          <a-descriptions title="申请信息" :column="2" size="small" style="margin-bottom: 16px;">
            <a-descriptions-item label="申请人">{{ applicantInfo.memberName }}</a-descriptions-item>
            <a-descriptions-item label="手机号">{{ applicantInfo.memberPhone }}</a-descriptions-item>
            <a-descriptions-item label="申请类型">
              <a-tag :color="getApplicationTypeColor(applicantInfo)">
                {{ applicantInfo.applicationType_dictText || getApplicationTypeText(applicantInfo) }}
              </a-tag>
            </a-descriptions-item>
            <a-descriptions-item label="支付金额">
              {{ applicantInfo.paymentAmount || '0' }} 助力值
            </a-descriptions-item>
            <a-descriptions-item label="支付状态">{{ applicantInfo.paymentStatus_dictText }}</a-descriptions-item>
            <a-descriptions-item label="推荐人">{{ applicantInfo.recommenderName }}</a-descriptions-item>
            <a-descriptions-item label="推荐人手机号">{{ applicantInfo.recommenderPhone }}</a-descriptions-item>
            <a-descriptions-item label="申请时间">{{ applicantInfo.createTime }}</a-descriptions-item>
          </a-descriptions>

          <a-descriptions title="申请理由" :column="1" size="small" style="margin-bottom: 16px;">
            <a-descriptions-item label="">{{ applicantInfo.applicationReason || '无' }}</a-descriptions-item>
          </a-descriptions>
        </div>

        <a-row>
          <a-col :span="24">
            <a-form-model-item label="审核结果" prop="auditStatus">
              <a-radio-group v-model="model.auditStatus" :disabled="true">
                <a-radio value="1">
                  <a-icon type="check-circle" style="color: #52c41a;" />
                  审核通过
                </a-radio>
                <a-radio value="2">
                  <a-icon type="close-circle" style="color: #ff4d4f;" />
                  审核拒绝
                </a-radio>
              </a-radio-group>
            </a-form-model-item>
          </a-col>
        </a-row>

        <a-row>
          <a-col :span="24">
            <a-form-model-item label="审核备注" prop="auditRemark">
              <a-textarea 
                v-model="model.auditRemark" 
                rows="4" 
                :placeholder="auditStatus === '1' ? '请输入通过原因（可选）' : '请输入拒绝原因'"
                :maxLength="500"
                show-count
              ></a-textarea>
            </a-form-model-item>
          </a-col>
        </a-row>

        <!-- 审核通过时的提示信息 -->
        <div v-if="auditStatus === '1'" class="audit-tips">
          <a-alert
            message="审核通过后的操作"
            :description="getPassDescription()"
            type="success"
            show-icon
          />
        </div>

        <!-- 审核拒绝时的提示信息 -->
        <div v-if="auditStatus === '2'" class="audit-tips">
          <a-alert
            message="审核拒绝后的操作"
            :description="getRejectDescription()"
            type="warning"
            show-icon
          />
        </div>

      </a-form-model>
    </a-spin>
  </a-modal>
</template>

<script>
  import { postApplicationAction } from '@/api/manage'

  export default {
    name: 'RankerApplicationAuditModal',
    data () {
      return {
        layout: {
          labelCol: {
            xs: { span: 24 },
            sm: { span: 4 },
          },
          wrapperCol: {
            xs: { span: 24 },
            sm: { span: 20 },
          },
        },
        title: "审核申请",
        visible: false,
        model: {},
        confirmLoading: false,
        applicantInfo: {},
        isBatch: false,
        selectedIds: [],
        auditStatus: '1',
        validatorRules: {
          auditRemark: [
            { 
              validator: (rule, value, callback) => {
                if (this.auditStatus === '2' && (!value || value.trim() === '')) {
                  callback(new Error('审核拒绝时必须填写拒绝原因'));
                } else {
                  callback();
                }
              }
            }
          ],
        },
        url: {
          audit: "/rankerApplication/rankerApplication/audit",
          batchAudit: "/rankerApplication/rankerApplication/batchAudit"
        }
      }
    },
    methods: {
      // 显示单个审核弹窗
      show (record, status) {
        this.reset();
        this.isBatch = false;
        this.auditStatus = status;
        this.applicantInfo = record;
        this.model = {
          applicationId: record.id,
          auditStatus: status,
          auditRemark: ''
        };
        this.visible = true;
      },
      
      // 显示批量审核弹窗
      showBatch (selectedIds, status) {
        this.reset();
        this.isBatch = true;
        this.auditStatus = status;
        this.selectedIds = selectedIds;
        this.model = {
          applicationIds: selectedIds.join(','),
          auditStatus: status,
          auditRemark: ''
        };
        this.visible = true;
      },

      reset() {
        this.$refs.form && this.$refs.form.resetFields();
        this.model = {};
        this.applicantInfo = {};
        this.selectedIds = [];
        this.confirmLoading = false;
      },

      close () {
        this.visible = false;
        this.reset();
      },

      handleOk () {
        const that = this;
        // 触发表单验证
        this.$refs.form.validate(valid => {
          if(valid) {
            that.confirmLoading = true;
            
            let httpurl = this.isBatch ? this.url.batchAudit : this.url.audit;
            let params = {};
            
            if (this.isBatch) {
              params = {
                applicationIds: this.model.applicationIds,
                applicationStatus: this.model.auditStatus,
                auditRemark: this.model.auditRemark
              };
            } else {
              params = {
                applicationId: this.model.applicationId,
                applicationStatus: this.model.auditStatus,
                auditRemark: this.model.auditRemark
              };
            }

            postApplicationAction(httpurl, params).then((res)=>{
              if(res.success){
                that.$message.success(res.message);
                that.$emit('ok');
                that.close();
              }else{
                that.$message.warning(res.message);
              }
            }).finally(() => {
              that.confirmLoading = false;
            })
          }
        })
      },

      handleCancel () {
        this.close()
      },

      // 获取申请类型文本
      getApplicationTypeText(record) {
        if (record.paymentAmount && parseFloat(record.paymentAmount) > 0) {
          return '付费申请';
        } else {
          return '审核申请';
        }
      },

      // 获取申请类型颜色
      getApplicationTypeColor(record) {
        if (record.paymentAmount && parseFloat(record.paymentAmount) > 0) {
          return 'orange'; // 付费申请用橙色
        } else {
          return 'blue'; // 审核申请用蓝色
        }
      },

      // 获取审核通过描述
      getPassDescription() {
        if (this.isBatch) {
          return '批量审核通过后，所有申请人将自动获得打榜者身份，可以参与曝光商品的分享和业绩计算。';
        }

        const isPaymentType = this.applicantInfo.paymentAmount && parseFloat(this.applicantInfo.paymentAmount) > 0;
        if (isPaymentType) {
          return '审核通过后，申请人将获得打榜者身份。注意：该申请为付费申请，申请人已支付费用。';
        } else {
          return '审核通过后，申请人将自动获得打榜者身份，可以参与曝光商品的分享和业绩计算。';
        }
      },

      // 获取审核拒绝描述
      getRejectDescription() {
        if (this.isBatch) {
          return '批量审核拒绝后，如果申请人已支付费用，系统将自动退还到其账户余额中。';
        }

        const isPaymentType = this.applicantInfo.paymentAmount && parseFloat(this.applicantInfo.paymentAmount) > 0;
        const hasPaid = this.applicantInfo.paymentStatus === '1';

        if (isPaymentType && hasPaid) {
          return `审核拒绝后，申请人已支付的 ${this.applicantInfo.paymentAmount} 助力值将自动退还到其账户余额中。`;
        } else if (isPaymentType && !hasPaid) {
          return '审核拒绝后，申请人无需支付费用。';
        } else {
          return '审核拒绝后，申请人可以重新提交申请。';
        }
      },
    }
  }
</script>

<style scoped>
.audit-info {
  margin-bottom: 16px;
}

.batch-audit-info {
  margin-bottom: 16px;
}

.audit-tips {
  margin-top: 16px;
}
</style>
