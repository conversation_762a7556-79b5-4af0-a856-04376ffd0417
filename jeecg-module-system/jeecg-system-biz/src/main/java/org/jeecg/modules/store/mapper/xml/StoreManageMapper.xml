<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.store.mapper.StoreManageMapper">
    <select id="getAllStoreList" resultType="map">
        SELECT sm.id,
               IF
               (
                       LENGTH(
                               TRIM(sm.`sub_store_name`)) &gt;
                       0,
                       CONCAT(sm.`store_name`, '(', ifnull(sm.`sub_store_name`, ''), ')'),
                       sm.`store_name`
               )             AS storeName,
               sm.boss_phone AS bossPhone,
               sm.sys_user_id AS sysUserId
        FROM `store_manage` sm
        WHERE sm.`status` = '1'
          AND sm.attestation_status IN (1, 2)
          AND sm.del_flag = '0'
        <if test="userId != null and userId != ''">
            AND sm.sys_user_id = #{userId}
        </if>
        <if test="franchiseeUserId != null and franchiseeUserId != ''">
            AND sm.alliance_user_id = #{franchiseeUserId}
        </if>
    </select>

    <select id="getAllStoreDictList" resultType="map">
        SELECT sm.sys_user_id as value,
               IF
               (
                       LENGTH(
                               TRIM(sm.`sub_store_name`)) &gt;
                       0,
                       CONCAT(sm.`store_name`, '(', ifnull(sm.`sub_store_name`, ''), ')'),
                       sm.`store_name`
               )              AS text,
               sm.boss_phone  AS bossPhone
        FROM `store_manage` sm
        WHERE sm.`status` = '1'
          AND sm.attestation_status IN (1, 2)
          AND sm.del_flag = '0'
        <if test="userId != null and userId != ''">
            AND sm.sys_user_id = #{userId}
        </if>
        <if test="franchiseeUserId != null and franchiseeUserId != ''">
            AND sm.alliance_user_id = #{franchiseeUserId}
        </if>
    </select>


    <select id="getStoreManageById" resultType="map">
        SELECT sm.id,
               IF
               (
                       LENGTH(
                               TRIM(sm.`sub_store_name`)) &gt; 0,
                       CONCAT(sm.`store_name`, '(', sm.`sub_store_name`, ')'),
                       sm.`store_name`
               )                                         AS storeName,
               sm.logo_addr                              AS logoAddr,
               CONCAT(sm.area_address, sm.store_address) AS storeAddress,
               sm.comprehensive_evaluation               AS comprehensiveEvaluation,
               sm.longitude,
               sm.sys_user_id                            AS sysUserId,
               sm.latitude,
               sm.STATUS,
               sm.is_open_welfare_payments               AS isOpenWelfarePayments,
               sm.if_view_welfare_payments               AS ifViewWelfarePayments,
               (SELECT COUNT(1)
                FROM `marketing_discount`
                WHERE STATUS = 1
                  AND del_flag = '0'
                  AND total &gt; released_quantity
                  AND sys_user_id = sm.sys_user_id)      AS disCount,
               sm.sort,
               sm.take_out_phone                         AS takeOutPhone,
               sm.introduce,
               sm.business_hours_explain                 AS businessHoursExplain,
               sm.according_store                        AS accordingStore,
               sm.store_picture                          AS storePicture,
               sm.product_wholesale_status               as productWholesaleStatus,
               sm.product_wholesale_title                as productWholesaleTitle,
               sm.product_wholesale_only_franchiser      as productWholesaleOnlyFranchiser,
               sm.product_wholesale_only_agency          as productWholesaleOnlyAgency,
               sm.products_selected_status               as productsSelectedStatus,
               sm.products_selected_title                as productsSelectedTitle,
               sm.products_selected_share_picture        as productsSelectedSharePicture,
               sm.product_wholesale_apply_picture        as productWholesaleApplyPicture,
               sm.product_wholesale_share_picture        as productWholesaleSharePicture,
               sm.products_selected_posts_picture        as productsSelectedPostsPicture,
               sm.apply_agency_btn                       as applyAgencyBtn,
               sm.apply_franchiser_btn                   as applyFranchiserBtn,
               sm.introduce                              as introduce,
               sm.total_performance                      as totalPerformance,
               sm.store_propaganda_images                as storePropagandaImages,
               sm.straight                               as storeStraight,
               sm.license_for_enterprise                 as licenseForEnterprise,
               sm.store_level                            AS storeLevel,
               (SELECT COUNT(DISTINCT gsl.id)
                FROM good_store_list gsl
                WHERE gsl.sys_user_id = sm.sys_user_id
                  AND gsl.del_flag = '0'
                  AND gsl.status = '1'
                  AND gsl.audit_status = '2'
                  AND gsl.frame_status = '1')            AS goodsCount,
               (SELECT IFNULL(SUM(gss.sales_volume), 0)
                FROM good_store_list gsl 
                LEFT JOIN good_store_specification gss ON gss.good_store_list_id = gsl.id
                WHERE gsl.sys_user_id = sm.sys_user_id 
                  AND gsl.del_flag = '0' 
                  AND gsl.status = '1'
                  AND gss.del_flag = '0')                AS salesVolume
        FROM `store_manage` sm
        WHERE sm.id = #{storeManageId}
          AND sm.del_flag = '0'
    </select>


    <select id="getStoreManageByRecommend" resultType="map">
        SELECT sm.id,
               IF
               (
                       LENGTH(
                               TRIM(sm.`sub_store_name`)) &gt; 0,
                       CONCAT(sm.`store_name`, '(', sm.`sub_store_name`, ')'),
                       sm.`store_name`
               )                                    AS storeName,
               sm.logo_addr                         AS logoAddr,
               sm.store_address                     AS storeAddress,
               sm.comprehensive_evaluation          AS comprehensiveEvaluation,
               sm.longitude,
               sm.sys_user_id                       AS sysUserId,
               sm.latitude,
               sm.STATUS,
               sm.is_open_welfare_payments          AS isOpenWelfarePayments,
               sm.if_view_welfare_payments          AS ifViewWelfarePayments,
               (SELECT COUNT(1)
                FROM `marketing_discount`
                WHERE STATUS = 1
                  AND del_flag = '0'
                  AND total &gt;
                      released_quantity
                  AND sys_user_id = sm.sys_user_id) AS disCount,
               POWER(POWER(sm.longitude - #{paramMap.longitude}, 2) + POWER(sm.latitude - #{paramMap.latitude}, 2),
                     1 / 2
               )                                    AS extent,
               sm.sort,
               sm.area_address                      AS areaAddress,
               sm.take_out_phone                    AS takeOutPhone,
               sm.introduce                         as introduce,
               sm.total_performance                 as totalPerformance
        FROM `store_manage` sm
        WHERE sm.`status` = '1'
          AND sm.attestation_status IN (1, 2)
          AND sm.del_flag = '0'
        <!--        AND sm.is_recommend='1'-->
        <if test="paramMap.type != null  and paramMap.type == 1">
            and sm.store_name in ('多彩贵州美酒', '贵州故事馆')
        </if>

        <if test="paramMap.pattern == 0">
            order by extent asc
        </if>

        <if test="paramMap.pattern == 1">
            order by sm.sort asc
        </if>
    </select>


    <select id="findStoreManageList" resultType="map" parameterType="map">
        SELECT sm.id,
               IF(
                       LENGTH(TRIM(sm.`sub_store_name`)) &gt; 0,
                       CONCAT(
                               sm.`store_name`,
                               '(',
                               sm.`sub_store_name`,
                               ')'
                       ),
                       sm.`store_name`
               )                                    AS storeName,
               sm.logo_addr                         AS logoAddr,
               sm.store_address                     AS storeAddress,
               sm.comprehensive_evaluation          AS comprehensiveEvaluation,
               sm.longitude,
               sm.sys_user_id                       as sysUserId,
               sm.latitude,
               sm.status,
               sm.is_open_welfare_payments          AS isOpenWelfarePayments,
               sm.if_view_welfare_payments          AS ifViewWelfarePayments,
               sm.introduce                         as introduce,
               sm.total_performance                 as totalPerformance,
               sm.store_level                       AS storeLevel,
               (SELECT item_text FROM sys_dict_item WHERE dict_id = (SELECT id FROM sys_dict WHERE dict_code = 'store_level') AND item_value = CAST(sm.store_level AS CHAR)) AS storeLevelName,
               SUBSTRING_INDEX(SUBSTRING_INDEX(sm.area_address, ',', 2), ',', -1) AS city,
               sm.main_type                         AS mainType,
               (SELECT item_text FROM sys_dict_item WHERE dict_id = (SELECT id FROM sys_dict WHERE dict_code = 'store_main_type') AND item_value = sm.main_type) AS mainTypeName,
               sm.store_type                        AS storeType,
               CASE sm.store_type
                   WHEN '15' THEN '品牌店'
                   WHEN '16' THEN '生活店'
                   WHEN '17' THEN '创业店'
                   ELSE ''
               END AS storeTypeName,
               sm.take_out_phone                    AS takeOutPhone,
               (SELECT COUNT(1)
                FROM `marketing_discount`
                WHERE STATUS = 1
                  AND del_flag = '0'
                  AND total &gt;
                      released_quantity
                  AND sys_user_id = sm.sys_user_id) AS disCount,
               POWER(POWER(sm.longitude - #{paramMap.longitude}, 2) + POWER(sm.latitude - #{paramMap.latitude}, 2),
                     1 / 2
               )                                    AS extent
        FROM `store_manage` sm
        WHERE sm.status = '1'
          AND sm.attestation_status IN (1, 2)
          AND sm.del_flag = '0'
        <if test="paramMap.itemValue != null and paramMap.itemValue != ''">
            AND sm.main_type = #{paramMap.itemValue}
        </if>
        <if test="paramMap.storeType != null and paramMap.storeType != ''">
            and sm.store_type = #{paramMap.storeType}
        </if>
        <if test="paramMap.storeName != null and paramMap.storeName != ''">
            and IF(
                        LENGTH(TRIM(sm.`sub_store_name`)) > 0,
                        CONCAT(
                                sm.`store_name`,
                                '(',
                                sm.`sub_store_name`,
                                ')'
                        ),
                        sm.`store_name`
                ) LIKE concat('%', concat(#{paramMap.storeName}, '%'))
        </if>
        <if test="paramMap.pattern == 0 or paramMap.pattern == 1">
            order by extent asc
        </if>
        <if test="paramMap.pattern == 2">
            ORDER BY disCount DESC
        </if>

        <if test="paramMap.pattern == 3">
            ORDER BY ifViewWelfarePayments DESC
        </if>
    </select>
    <select id="findStoreManage" resultType="org.jeecg.modules.store.dto.StoreManageDTO">
        SELECT sm.*
        FROM store_manage sm
        WHERE sm.`del_flag` = '0'
          AND sm.`status` = 1
        <if test="storeManageDTO != null and storeManageDTO.storeName != null and storeManageDTO.storeName != ''">
            AND sm.store_name LIKE concat('%', concat(#{storeManageDTO.storeName}, '%'))
        </if>
        ORDER BY sm.`create_time` DESC
    </select>
    <select id="findStore" resultType="org.jeecg.modules.store.vo.StoreManageVO">
        SELECT sm.`id`,
               sm.`store_name`,
               sm.`sub_store_name`,
               sm.`logo_addr`,
               sm.`store_picture`,
               sm.`service_range`,
               sm.`take_out_phone`,
               ss.`address`,
               sm.if_view_welfare_payments,
               sm.is_view_vip_price,
               su.`username` AS userName,
               sm.business_hours_explain,
               sm.introduce  as introduce,
               sm.store_propaganda_images as storePropagandaImages
        FROM store_manage sm
                 LEFT JOIN sys_smallcode ss
                           ON sm.sys_smallcode_id = ss.`id`
                 LEFT JOIN sys_user su
                           ON su.`id` = sm.`sys_user_id`
        WHERE sm.`del_flag` = '0'
          AND sm.`sys_user_id` = #{userId}
    </select>
    <select id="queryStoreManagePage" resultType="org.jeecg.modules.store.dto.StoreManageDTO"
            parameterType="org.jeecg.modules.store.vo.StoreManageVO">
        SELECT * FROM (
        SELECT sm.*,
               (select count(1) from store_label_relation where store_manage_id = sm.id)             as labelCount,
               ss.`address`                                                                          AS ssAddress,
               su.`username`                                                                         AS userName,
               (
                   CASE
                       sm.`promoter_type`
                       WHEN 0
                           THEN
                           (SELECT IF(
                                           LENGTH(TRIM(sms.`sub_store_name`)) > 0,
                                           CONCAT(
                                                   sms.`store_name`,
                                                   '(',
                                                   sms.`sub_store_name`,
                                                   ')'
                                           ),
                                           sms.`store_name`
                                   )
                            FROM store_manage sms
                            WHERE sms.`sys_user_id` = sm.`promoter`)
                       WHEN 1
                           THEN
                           (SELECT IF(
                                           ml.`nick_name` IS NULL,
                                           ml.`phone`,
                                           IF(
                                                   ml.`phone` IS NULL,
                                                   ml.`nick_name`,
                                                   CONCAT(
                                                           ml.`nick_name`,
                                                           '(',
                                                           ml.`phone`,
                                                           ')'
                                                   )
                                           )
                                   )
                            FROM member_list ml
                            WHERE ml.`del_flag` = '0'
                              AND ml.`id` = sm.`promoter`)
                       WHEN 2
                           THEN '平台'
                       WHEN 3
                           THEN
                           (SELECT CONCAT(am.`name`, '(', su.`realname`, ')')
                            FROM alliance_manage am
                                     LEFT JOIN sys_user su
                                               ON am.`sys_user_id` = su.`id`
                            WHERE am.`del_flag` = '0'
                              AND am.`sys_user_id` = sm.`promoter`)
                       ELSE '无'
                       END
                   )                                                                                 AS promoterName,
               if(sm.alliance_user_id IS NULL, '无', (SELECT CONCAT(am.`name`, '(', su.`realname`, ')')
                                                      FROM alliance_manage am
                                                               LEFT JOIN sys_user su
                                                                         ON am.`sys_user_id` = su.`id`
                                                      WHERE am.`del_flag` = '0'
                                                        AND am.`sys_user_id` = sm.alliance_user_id)) AS allianceName,
               IF(
                       LENGTH(TRIM(ssm.`sub_store_name`)) > 0,
                       CONCAT(
                               ssm.`store_name`,
                               '(',
                               ssm.`sub_store_name`,
                               ')'
                       ),
                       ssm.`store_name`
               )                                                                                     as storeSizeTManageName
        FROM store_manage sm
                 LEFT JOIN sys_smallcode ss
                           ON sm.`sys_smallcode_id` = ss.`id`
                 LEFT JOIN sys_user su
                           ON su.`id` = sm.`sys_user_id`
                 left join store_manage ssm on sm.store_size_t_manage_id = ssm.id
        WHERE sm.`del_flag` = '0'
          AND sm.`pay_status` IN (1, 2)
        <if test="storeManageVO != null and storeManageVO.id != null and storeManageVO.id != ''">
            AND sm.id LIKE concat('%', concat(#{storeManageVO.id}, '%'))
        </if>
        <if test="storeManageVO.ids != null  and storeManageVO.ids != ''">
            and sm.id in
            <foreach item="item" index="index" collection="storeManageVO.ids.split(',')" open="(" separator=","
                     close=")">
                #{item}
            </foreach>
        </if>
        <if test="storeManageVO != null and storeManageVO.userName != null and storeManageVO.userName != ''">
            AND su.username LIKE concat('%', concat(#{storeManageVO.userName}, '%'))
        </if>
        <if test="storeManageVO != null and storeManageVO.bossName != null and storeManageVO.bossName != ''">
            AND sm.boss_name LIKE concat('%', concat(#{storeManageVO.bossName}, '%'))
        </if>
        <if test="storeManageVO != null and storeManageVO.bossPhone != null and storeManageVO.bossPhone != ''">
            AND sm.boss_phone LIKE concat('%', concat(#{storeManageVO.bossPhone}, '%'))
        </if>
        <if test="storeManageVO != null and storeManageVO.storeName != null and storeManageVO.storeName != ''">
            AND sm.store_name LIKE concat('%', concat(#{storeManageVO.storeName}, '%'))
        </if>
        <if test="storeManageVO != null and storeManageVO.subStoreName != null and storeManageVO.subStoreName != ''">
            AND sm.sub_store_name LIKE concat('%', concat(#{storeManageVO.subStoreName}, '%'))
        </if>
        <if test="storeManageVO != null and storeManageVO.straight != null and storeManageVO.straight != ''">
            AND sm.straight = #{storeManageVO.straight}
        </if>
        <if test="storeManageVO != null and storeManageVO.mainType != null and storeManageVO.mainType != ''">
            AND sm.main_type = #{storeManageVO.mainType}
        </if>
        <if test="storeManageVO.storeType != null and storeManageVO.storeType != ''">
            AND sm.store_type = #{storeManageVO.storeType}
        </if>
        <if test="storeManageVO != null and storeManageVO.promoterType != null and storeManageVO.promoterType != ''">
            AND sm.promoter_type = #{storeManageVO.promoterType}
        </if>
        <if test="storeManageVO != null and storeManageVO.attestationStatus != null and storeManageVO.attestationStatus != ''">
            AND sm.attestation_status = #{storeManageVO.attestationStatus}
        </if>

        <if test="storeManageVO != null and storeManageVO.sysAreaId != null and storeManageVO.sysAreaId != ''">
            AND sm.sys_area_id LIKE concat('%', #{storeManageVO.sysAreaId}, '%')
        </if>

        <if test="storeManageVO != null and storeManageVO.ProvincialId != null and storeManageVO.ProvincialId != ''">
            AND FIND_IN_SET(SUBSTRING_INDEX(sm.`sys_area_id`, ',', 1), (SELECT am.`sys_area_id`
                                                                        FROM agency_manage am
                                                                        WHERE am.`del_flag` = '0'
                                                                          AND am.`sys_user_id` = #{storeManageVO.ProvincialId}))
        </if>
        <if test="storeManageVO != null and storeManageVO.municipalId != null and storeManageVO.municipalId != ''">
            AND FIND_IN_SET(SUBSTRING_INDEX(SUBSTRING_INDEX(sm.`sys_area_id`, ',', 2), ',', -1),
                            (SELECT am.`sys_area_id`
                             FROM agency_manage am
                             WHERE am.`del_flag` = '0'
                               AND am.`sys_user_id` = #{storeManageVO.municipalId}))
        </if>
        <if test="storeManageVO != null and storeManageVO.countyId != null and storeManageVO.countyId != ''">
            AND FIND_IN_SET(SUBSTRING_INDEX(sm.`sys_area_id`, ',', -1), (SELECT am.`sys_area_id`
                                                                         FROM agency_manage am
                                                                         WHERE am.`del_flag` = '0'
                                                                           AND am.`sys_user_id` = #{storeManageVO.countyId}))
        </if>
        ORDER BY sm.`create_time` DESC
        ) m

        <if test="storeManageVO != null and storeManageVO.promoterName != null and storeManageVO.promoterName != ''">
            WHERE m.promoterName LIKE concat('%', #{storeManageVO.promoterName}, '%')
        </if>
    </select>
    <select id="findPageList" resultType="org.jeecg.modules.store.dto.StoreManageDTO">
        SELECT sm.*,
               ss.`address`  AS ssAddress,
               su.`username` AS userName
        FROM store_manage sm
                 LEFT JOIN sys_smallcode ss
                           ON sm.`sys_smallcode_id` = ss.`id`
                 LEFT JOIN sys_user su
                           ON su.`id` = sm.`sys_user_id`
        WHERE sm.`del_flag` = '0'
          AND sm.`pay_status` != 0
        <if test="storeManageVO != null and storeManageVO.bossName != null and storeManageVO.bossName != ''">
            AND sm.boss_name LIKE concat('%', concat(#{storeManageVO.bossName}, '%'))
        </if>
        <if test="storeManageVO != null and storeManageVO.bossPhone != null and storeManageVO.bossPhone != ''">
            AND sm.boss_phone LIKE concat('%', concat(#{storeManageVO.bossPhone}, '%'))
        </if>
        <if test="storeManageVO != null and storeManageVO.storeName != null and storeManageVO.storeName != ''">
            AND sm.store_name LIKE concat('%', concat(#{storeManageVO.storeName}, '%'))
        </if>
        <if test="storeManageVO != null and storeManageVO.subStoreName != null and storeManageVO.subStoreName != ''">
            AND sm.sub_store_name LIKE concat('%', concat(#{storeManageVO.subStoreName}, '%'))
        </if>
        <if test="storeManageVO != null and storeManageVO.straight != null and storeManageVO.straight != ''">
            AND sm.straight = #{storeManageVO.straight}
        </if>
        <if test="storeManageVO != null and storeManageVO.mainType != null and storeManageVO.mainType != ''">
            AND sm.main_type = #{storeManageVO.mainType}
        </if>
        <if test="storeManageVO != null and storeManageVO.attestationStatus != null and storeManageVO.attestationStatus != ''">
            AND sm.attestation_status = #{storeManageVO.attestationStatus}
        </if>
        ORDER BY sm.`create_time` DESC
    </select>
    <select id="findStoreBalance" resultType="org.jeecg.modules.store.dto.StoreManageDTO">
        SELECT * from (
        SELECT sm.id,
               su.`username`                       AS userName,
               IF(
                       LENGTH(TRIM(sm.`sub_store_name`)) > 0,
                       CONCAT(
                               sm.`store_name`,
                               '(',
                               sm.`sub_store_name`,
                               ')'
                       ),
                       sm.`store_name`
               )                                   AS storeName,
               sm.`area_address`,
               sm.`boss_name`,
               sm.`boss_phone`,
               sm.`balance`,
               sm.status,
               if(sm.status = '0', '停用', '启用') AS statusName
        FROM store_manage sm
                 LEFT JOIN sys_user su
                           ON sm.`sys_user_id` = su.`id`
        WHERE sm.`del_flag` = '0'
          AND sm.`pay_status` IN (1, 2)
        <if test="storeManageVO != null and storeManageVO.userName != null and storeManageVO.userName != ''">
            AND su.username LIKE concat('%', concat(#{storeManageVO.userName}, '%'))
        </if>
        <if test="storeManageVO != null and storeManageVO.bossName != null and storeManageVO.bossName != ''">
            AND sm.boss_name LIKE concat('%', concat(#{storeManageVO.bossName}, '%'))
        </if>
        <if test="storeManageVO != null and storeManageVO.bossPhone != null and storeManageVO.bossPhone != ''">
            AND sm.boss_phone LIKE concat('%', concat(#{storeManageVO.bossPhone}, '%'))
        </if>
        <if test="storeManageVO != null and storeManageVO.status != null and storeManageVO.status != ''">
            AND sm.status = #{storeManageVO.status}
        </if>
        <if test="storeManageVO != null and storeManageVO.areaAddress != null and storeManageVO.areaAddress != ''">
            AND sm.area_address LIKE CONCAT('%', #{storeManageVO.areaAddress}, '%')
        </if>
        ORDER BY sm.`create_time` DESC
        ) m
        <if test="storeManageVO != null and storeManageVO.storeName != null and storeManageVO.storeName != ''">
            WHERE m.storeName LIKE concat('%', concat(#{storeManageVO.storeName}, '%'))
        </if>
    </select>
    <select id="findStoreWorkbenchVO" resultType="org.jeecg.modules.store.vo.StoreWorkbenchVO">
        SELECT sm.id,                                                         -- 店铺id
               sm.sys_user_id,
               sm.`logo_addr`,
               -- logo
               sm.`boss_name`,
               --   -- 联系人
               CONCAT(
                       sm.`store_name`,
                       '(',
                       sm.`sub_store_name`,
                       ')'
               )                                            AS storeName,
               --   -- 店铺名称
               sr.`role_name`,
               --   -- 角色
               sm.`attestation_status`,
               --   -- 认证状态
               (
                   CASE
                       sm.`open_type`
                       WHEN 0
                           THEN CONCAT(
                               '包年(',
                               sm.`end_time`,
                               '到期)'
                                )
                       WHEN 1
                           THEN '终生'
                       ELSE ''
                       END
                   )                                        AS openUp,
               --   -- 开通
               sm.`status`,
               --   -- 状态
               (
                   CASE
                       sm.`status`
                       WHEN 0
                           THEN '停用'
                       WHEN 1
                           THEN '启用'
                       ELSE '异常'
                       END
                   )                                        AS statusName,
               --   --  状态备注
               (SELECT COUNT(1)
                FROM order_store_list osl
                WHERE osl.`del_flag` = '0'
                  AND osl.`status` IN (1, 2, 3)
                  AND osl.`sys_user_id` = sm.`sys_user_id`) AS orderSum,
               --   -- 有效订单
               (SELECT SUM(sac.`amount`)
                FROM store_account_capital sac
                WHERE sac.`del_flag` = '0'
                  AND sac.`go_and_come` = 0
                  AND sac.`store_manage_id` = sm.`id`)      AS earnings,
               --   -- 累计收入
               (SELECT COUNT(1)
                FROM member_list ml
                WHERE ml.`del_flag` = '0'
                  AND ml.`sys_user_id` = sm.`sys_user_id`)  AS memberSum,
               --   -- 累计会员
               (SELECT SUM(mwp.`bargain_payments`)
                FROM marketing_welfare_payments mwp
                WHERE mwp.`del_flag` = '0'
                  AND mwp.`is_platform` = 0
                  AND mwp.`status` = 1
                  AND mwp.`sys_user_id` = sm.`sys_user_id`) AS welfareSum,
               --   -- 已送福利金
               (SELECT COUNT(1)
                FROM order_store_list osl
                WHERE osl.`del_flag` = '0'
                  AND osl.`status` = 1
                  AND osl.`sys_user_id` = sm.`sys_user_id`) AS waitDeliverGoods,
               --   -- 待发货
               (SELECT COUNT(1)
                FROM good_store_list gsl
                WHERE gsl.`del_flag` = '0'
                  AND gsl.`sys_user_id` = sm.`sys_user_id`
                  AND gsl.`status` = 1
                  AND gsl.`frame_status` = 1
                  AND gsl.`audit_status` = 2)               AS forGoods,
               --   -- 在售商品
               (SELECT COUNT(1)
                FROM marketing_discount_coupon mdc
                WHERE mdc.`del_flag` = '0'
                  AND mdc.`is_platform` = 0
                  AND mdc.`sys_user_id` = sm.`sys_user_id`) AS byGetDiscount,
               --   -- 已被领取优惠券
               (SELECT COUNT(1)
                FROM marketing_discount_coupon mdc
                WHERE mdc.`del_flag` = '0'
                  AND mdc.`is_platform` = 0
                  AND mdc.`sys_user_id` = sm.`sys_user_id`
                  AND mdc.`status` = 2)                     AS byUseDiscount, -- 已被使用优惠券
               (SELECT COUNT(1)
                FROM member_list ml
                WHERE ml.`del_flag` = '0'
                  AND ml.member_type = 0
                  AND ml.`sys_user_id` = sm.`sys_user_id`)  AS memberP,
               (SELECT COUNT(1)
                FROM member_list ml
                WHERE ml.`del_flag` = '0'
                  AND ml.member_type = 1
                  AND ml.`sys_user_id` = sm.`sys_user_id`)  AS memberV
        FROM store_manage sm
                 LEFT JOIN sys_user_role sur
                           ON sm.`sys_user_id` = sur.`user_id`
                 LEFT JOIN sys_role sr
                           ON sur.`role_id` = sr.`id`
        WHERE sm.`del_flag` = '0'
          AND sm.`sys_user_id` = #{userId}
    </select>
    <select id="myStore" resultType="map">
        SELECT sm.`logo_addr`          AS logoAddr,
               sm.`store_name`         AS storeName,
               (
                   CASE
                       sm.`open_type`
                       WHEN 0
                           THEN '包年'
                       WHEN 1
                           THEN '终生'
                       ELSE ''
                       END
                   )                   AS openType,
               (
                   CASE
                       sm.`open_type`
                       WHEN 0
                           THEN CONCAT('(', DATE_FORMAT(sm.`end_time`, '%Y-%m-%d'), '到期', ')')
                       WHEN 1
                           THEN NULL
                       ELSE ''
                       END
                   )                   AS endTime,
               (
                   CASE
                       sm.`attestation_status`
                       WHEN - 1
                           THEN '未认证'
                       WHEN 0
                           THEN '待审核'
                       WHEN 1
                           THEN '已认证'
                       WHEN 2
                           THEN '免认证'
                       WHEN 3
                           THEN '未通过'
                       WHEN 4
                           THEN '过期'
                       ELSE '异常'
                       END
                   )                   AS statusName,
               sm.`attestation_status` AS attestationStatus,
               su.`username`
        FROM store_manage sm
                 LEFT JOIN sys_user su
                           ON sm.`sys_user_id` = su.`id`
        WHERE sm.`del_flag` = '0'
          AND sm.`sys_user_id` = #{sysUserId}
    </select>

    <select id="findStoreInfo" resultType="map">
        SELECT sm.`id`,
               su.`username`,
               sm.`store_name`                            AS storeName,
               sm.`sub_store_name`                        AS subStoreName,
               sm.`logo_addr`                             AS logoAddr,
               sm.`store_picture`                         AS storePicture,
               sm.`service_range`                         AS serviceRange,
               sm.`take_out_phone`                        AS takeOutPhone,
               sm.`business_hours_explain`                AS businessHoursExplain,
               sm.`if_view_welfare_payments`              AS ifViewWelfarePayments,
               sm.`is_view_vip_price`                     AS isViewVipPrice,
               CONCAT(sm.`latitude`, ',', sm.`longitude`) AS longitudeAndLatitude
        FROM store_manage sm
                 LEFT JOIN sys_user su
                           ON sm.`sys_user_id` = su.`id`
        WHERE sm.`del_flag` = '0'
          AND sm.`sys_user_id` = #{sysUserId}
    </select>
    <select id="returnAuthentication" resultType="map">
        SELECT sm.`id`,
               sm.`store_name`             AS storeName,
               sm.`sub_store_name`         AS subStoreName,
               sm.`sys_area_id`            AS sysAreaId,
               sm.`area_address`           AS areaAddress,
               sm.`store_address`          AS storeAddress,
               sm.`straight`,
               sm.`main_type`              AS mainType,
               sm.`take_out_phone`         AS takeOutPhone,
               sm.`logo_addr`              AS logoAddr,
               sm.`store_picture`          AS storePicture,
               sm.`according_store`        AS accordingStore,
               sm.`social_credit_code`     AS socialCreditCode,
               sm.`license_for_enterprise` AS licenseForEnterprise,
               sm.`agent_type`             AS agentType,
               sm.`agent_name`             AS agentName,
               sm.`id_code`                AS idCode,
               sm.`id_picture_z`           AS idPictureZ,
               sm.`id_picture_f`           AS idPictureF,
               sm.`id_hand`                AS idHand,
               sm.`agent_authorization`    AS agentAuthorization
        FROM store_manage sm
        WHERE sm.`del_flag` = '0'
          AND sm.`sys_user_id` = #{sysUserId}
    </select>
    <select id="returnSecurity" resultType="map">
        SELECT su.`username` AS userName,
               su.`phone`
        FROM store_manage sm
                 LEFT JOIN sys_user su
                           ON sm.`sys_user_id` = su.`id`
        WHERE sm.`del_flag` = '0'
          AND sm.`sys_user_id` = #{sysUserId}
    </select>
    <select id="findCertificateStore" resultType="map">
        SELECT *
        FROM
        (SELECT IF(
                        LENGTH(TRIM(sm.`sub_store_name`)) > 0,
                        CONCAT(
                                sm.`store_name`,
                                '(',
                                sm.`sub_store_name`,
                                ')'
                        ),
                        sm.`store_name`
                )                   AS storeName,
                sm.`store_address`  AS storeAddress,
                sm.`longitude`,
                sm.`latitude`,
                sm.`take_out_phone` AS takeOutPhone,
                sm.logo_addr        AS logoAddr,
                POWER(
                        POWER(sm.longitude - #{objectObjectHashMap.longitude}, 2) +
                        POWER(sm.latitude - #{objectObjectHashMap.latitude},
                              2),
                        1 / 2
                )                   AS extent,
                sm.sys_user_id      AS sysUserId
         FROM `store_manage` sm
        WHERE sm.del_flag = 0
          AND sm.`status` = 1
          AND sm.`attestation_status` IN (1, 2)
        <if test="objectObjectHashMap.id != null and objectObjectHashMap.id != ''">
            AND sm.sys_user_id IN (SELECT mcs.`sys_user_id`
                                   FROM marketing_certificate_store mcs
                                   WHERE mcs.`del_flag` = '0'
                                     AND mcs.`marketing_certificate_id` = #{objectObjectHashMap.id})
        </if>
        ) m
        <if test="objectObjectHashMap.sysUserId != null and objectObjectHashMap.sysUserId != ''">
            WHERE m.sysUserId = #{objectObjectHashMap.sysUserId}
        </if>
        ORDER BY m.extent ASC
    </select>

    <select id="findUseInfo" resultType="map">
        SELECT
               sm.id,
               sm.`balance`,
               sm.`account_frozen`         AS accountFrozen,
               sm.`unusable_frozen`        AS unusableFrozen,
               sm.is_open_welfare_payments AS isOpenWelfarePayments,
               sm.money_receiving_code     as moneyReceivingCode,
               (SELECT
                    IFNULL(SUM(swd.money), 0)
                FROM
                    store_withdraw_deposit swd
                WHERE
                    swd.store_manage_id = sm.id
                    AND swd.del_flag = '0'
                    AND swd.status = '2'
               ) AS haveWithdrawal,
               (SELECT
                    IFNULL(SUM(swd.money), 0)
                FROM
                    store_withdraw_deposit swd
                WHERE
                    swd.store_manage_id = sm.id
                    AND swd.del_flag = '0'
                    AND swd.status IN ('0', '1')
               ) AS withdrawingAmount,
               (SELECT
                    IFNULL(SUM(osl.goods_total), 0)
                FROM
                    order_store_list osl
                WHERE
                    osl.sys_user_id = sm.sys_user_id
                    AND osl.del_flag = '0'
                    AND osl.status IN ('3', '5')
                    AND DATE(osl.delivery_time) = CURDATE()
               ) AS todaySalesAmount,
               (SELECT
                    COUNT(1)
                FROM
                    order_store_list osl
                WHERE
                    osl.sys_user_id = sm.sys_user_id
                    AND osl.del_flag = '0' and osl.status != '0' and (osl.close_type != '0' or osl.close_type is null)
                    AND DATE(osl.create_time) = CURDATE()
               ) AS todayOrderCount,
               (SELECT
                    IFNULL(SUM(osl.actually_received_amount), 0)
                FROM
                    order_store_list osl
                WHERE
                    osl.sys_user_id = sm.sys_user_id
                    AND osl.del_flag = '0'
                    AND osl.status IN ('3', '5')
               ) AS totalPerformance
        FROM store_manage sm
        WHERE sm.`del_flag` = '0'
          AND sm.`sys_user_id` = #{sysUserId}
    </select>
    <select id="findAllianceStoreList" resultType="org.jeecg.modules.store.vo.StoreManageVO">
        SELECT sm.*,
               ss.`address`  AS ssAddress,
               su.`username` AS userName,
               (
                   CASE
                       sm.`promoter_type`
                       WHEN 0
                           THEN
                           (SELECT IF(
                                           LENGTH(TRIM(sms.`sub_store_name`)) > 0,
                                           CONCAT(
                                                   sms.`store_name`,
                                                   '(',
                                                   sms.`sub_store_name`,
                                                   ')'
                                           ),
                                           sms.`store_name`
                                   )
                            FROM store_manage sms
                            WHERE sms.`sys_user_id` = sm.`promoter`)
                       WHEN 1
                           THEN
                           (SELECT IF(
                                           ml.`nick_name` IS NULL,
                                           ml.`phone`,
                                           IF(
                                                   ml.`phone` IS NULL,
                                                   ml.`nick_name`,
                                                   CONCAT(
                                                           ml.`nick_name`,
                                                           '(',
                                                           ml.`phone`,
                                                           ')'
                                                   )
                                           )
                                   )
                            FROM member_list ml
                            WHERE ml.`del_flag` = '0'
                              AND ml.`id` = sm.`promoter`)
                       WHEN 2
                           THEN '平台'
                       WHEN 3
                           THEN
                           (SELECT CONCAT(am.`name`, '(', su.`realname`, ')')
                            FROM alliance_manage am
                                     LEFT JOIN sys_user su
                                               ON am.`sys_user_id` = su.`id`
                            WHERE am.`del_flag` = '0'
                              AND am.`sys_user_id` = sm.`alliance_user_id`)
                       ELSE '无'
                       END
                   )         AS promoterName
        FROM store_manage sm
                 LEFT JOIN sys_smallcode ss
                           ON sm.`sys_smallcode_id` = ss.`id`
                 LEFT JOIN sys_user su
                           ON su.`id` = sm.`sys_user_id`
        WHERE sm.`del_flag` = '0'
          AND sm.`pay_status` IN (1, 2)
          AND LENGTH(TRIM(sm.`alliance_user_id`)) > 0
        <if test="storeManageDTO != null and storeManageDTO.bossName != null and storeManageDTO.bossName != ''">
            AND sm.boss_name LIKE concat('%', concat(#{storeManageDTO.bossName}, '%'))
        </if>
        <if test="storeManageDTO != null and storeManageDTO.bossPhone != null and storeManageDTO.bossPhone != ''">
            AND sm.boss_phone LIKE concat('%', concat(#{storeManageDTO.bossPhone}, '%'))
        </if>
        <if test="storeManageDTO != null and storeManageDTO.storeName != null and storeManageDTO.storeName != ''">
            AND sm.store_name LIKE concat('%', concat(#{storeManageDTO.storeName}, '%'))
        </if>
        <if test="storeManageDTO != null and storeManageDTO.subStoreName != null and storeManageDTO.subStoreName != ''">
            AND sm.sub_store_name LIKE concat('%', concat(#{storeManageDTO.subStoreName}, '%'))
        </if>
        <if test="storeManageDTO != null and storeManageDTO.straight != null and storeManageDTO.straight != ''">
            AND sm.straight = #{storeManageDTO.straight}
        </if>
        <if test="storeManageDTO != null and storeManageDTO.mainType != null and storeManageDTO.mainType != ''">
            AND sm.main_type = #{storeManageDTO.mainType}
        </if>
        <if test="storeManageDTO != null and storeManageDTO.attestationStatus != null and storeManageDTO.attestationStatus != ''">
            AND sm.attestation_status = #{storeManageDTO.attestationStatus}
        </if>
        <if test="storeManageDTO != null and storeManageDTO.promoter != null and storeManageDTO.promoter != ''">
            AND sm.alliance_user_id = #{storeManageDTO.promoter}
        </if>
        ORDER BY sm.`create_time` DESC
    </select>
    <select id="getStoreByBossPhone" resultType="map">
        SELECT sm.`id`,
               IF(
                       LENGTH(TRIM(sm.`sub_store_name`)) > 0,
                       CONCAT(
                               sm.`store_name`,
                               '(',
                               sm.`sub_store_name`,
                               ')'
                       ),
                       sm.`store_name`
               ) AS NAME,
               sm.boss_phone,
               sm.sys_user_id
        FROM store_manage sm
        WHERE sm.`del_flag` = '0'
          AND sm.status = 1
          AND sm.attestation_status IN (1, 2)
          AND sm.pay_status IN (1, 2)
        <if test="bossPhone != null and bossPhone != ''">
            AND sm.`boss_phone` LIKE CONCAT('%', #{bossPhone}, '%')
        </if>
        LIMIT 10
    </select>

    <select id="findStoreInfoByStoreName" resultType="map">
        SELECT *
        FROM (SELECT sm.`id`                                 AS storeManageId,
                     IF(
                             LENGTH(TRIM(sm.`sub_store_name`)) > 0,
                             CONCAT(
                                     sm.`store_name`,
                                     '(',
                                     sm.`sub_store_name`,
                                     ')'
                             ),
                             sm.`store_name`
                     )                                       AS storeName,
                     su.`username`,
                     sm.`boss_name`                          AS bossName,
                     sm.`boss_phone`                         AS bossPhone,
                     sm.`area_address`                       AS areaAddress,
                     sm.`store_address`                      AS storeAddress,
                     (SELECT s.item_text
                      FROM sys_dict_item s
                      WHERE s.dict_id =
                            (SELECT id
                             FROM sys_dict
                             WHERE dict_code = 'store_main_type')
                        AND s.`item_value` = sm.`main_type`) AS mainType
              FROM store_manage sm
                       LEFT JOIN sys_user su
                                 ON sm.`sys_user_id` = su.`id`
              WHERE sm.`del_flag` = '0'
                AND sm.`pay_status` IN (1, 2)
                AND sm.`attestation_status` IN (1, 2)) m
        <if test="storeName != null">
            WHERE m.storeName LIKE concat('%', #{storeName}, '%')
        </if>
        LIMIT 20
    </select>
    <select id="findCityLifeStoreManageList" resultType="map">
        SELECT *
        FROM
        (SELECT sm.id,
                IF(
                        LENGTH(TRIM(sm.`sub_store_name`)) > 0,
                        CONCAT(
                                sm.`store_name`,
                                '(',
                                sm.`sub_store_name`,
                                ')'
                        ),
                        sm.`store_name`
                )                                    AS storeName,
                sm.logo_addr                         AS logoAddr,
                sm.store_address                     AS storeAddress,
                sm.comprehensive_evaluation          AS comprehensiveEvaluation,
                sm.longitude,
                sm.sys_user_id                       as sysUserId,
                sm.latitude,
                sm.status,
                sm.is_open_welfare_payments          AS isOpenWelfarePayments,
                sm.if_view_welfare_payments          AS ifViewWelfarePayments,
                (SELECT COUNT(1)
                 FROM `marketing_discount`
                 WHERE STATUS = 1
                   AND del_flag = '0'
                   AND total &gt; released_quantity
                   AND sys_user_id = sm.sys_user_id) AS disCount,
                POWER(
                        POWER(sm.longitude - #{paramObjectMap.longitude}, 2) +
                        POWER(sm.latitude - #{paramObjectMap.latitude}, 2),
                        1 / 2
                )                                    AS extent
         FROM store_welfare_payments_gathering swpg
                  LEFT JOIN
              `store_manage` sm
              ON swpg.store_manage_id = sm.id
        WHERE swpg.del_flag = 0
          AND sm.status = '1'
          AND sm.attestation_status IN (1, 2)
          AND sm.del_flag = '0'
        <if test="paramObjectMap.itemValue != null and paramObjectMap.itemValue != ''">
            AND sm.main_type = #{paramObjectMap.itemValue}
        </if>
        <if test="paramObjectMap.storeTypeId != null  and paramObjectMap.storeTypeId != ''">
            and sm.store_type_id = #{paramObjectMap.storeTypeId}
        </if>
        ) m
        <if test="paramObjectMap.storeName != null and paramObjectMap.storeName != ''">
            WHERE m.storeName LIKE concat('%', concat(#{paramObjectMap.storeName}, '%'))
        </if>
        <if test="paramObjectMap.pattern == 0">
            ORDER BY m.extent ASC,
                     m.disCount,
                     m.comprehensiveEvaluation DESC
        </if>

        <if test="paramObjectMap.pattern == 1">
            ORDER BY m.extent ASC
        </if>


        <if test="paramObjectMap.pattern == 2">
            ORDER BY disCount DESC
        </if>

        <if test="paramObjectMap.pattern == 3">
            ORDER BY comprehensiveEvaluation DESC
        </if>
    </select>

    <select id="findCityLifeStoreManageListNew" resultType="map">
        SELECT *
        FROM
        (SELECT sm.id,
                IF(
                        LENGTH(TRIM(sm.`sub_store_name`)) > 0,
                        CONCAT(
                                sm.`store_name`,
                                '(',
                                sm.`sub_store_name`,
                                ')'
                        ),
                        sm.`store_name`
                )                                                                AS storeName,
                sm.logo_addr                                                     AS logoAddr,
                sm.store_address                                                 AS storeAddress,
                sm.comprehensive_evaluation                                      AS comprehensiveEvaluation,
                sm.longitude,
                sm.sys_user_id                                                   as sysUserId,
                sm.latitude,
                sm.status,
                sm.is_open_welfare_payments                                      AS isOpenWelfarePayments,
                sm.if_view_welfare_payments                                      AS ifViewWelfarePayments,
                concat('到店福利金抵扣', swpg.store_small_welfare_payments, '%') AS storeSmallWelfarePayments,
                if((SELECT COUNT(1)
                    FROM marketing_comming_store mcs
                    WHERE mcs.`del_flag` = '0'
                      AND mcs.`status` = 1
                      AND mcs.take_way = 0
                      AND mcs.`store_manage_id` = sm.id) > 0, 1, 0)              AS takeWayOne,
                if((SELECT COUNT(1)
                    FROM marketing_comming_store mcs
                    WHERE mcs.`del_flag` = '0'
                      AND mcs.`status` = 1
                      AND mcs.take_way = 1
                      AND mcs.`store_manage_id` = sm.id) > 0, 1, 0)              AS takeWayTwo,
                (SELECT COUNT(1)
                 FROM `marketing_discount`
                 WHERE STATUS = 1
                   AND del_flag = '0'
                   AND total &gt; released_quantity
                   AND sys_user_id = sm.sys_user_id)                             AS disCount,
                POWER(
                        POWER(sm.longitude - #{paramObjectMap.longitude}, 2) +
                        POWER(sm.latitude - #{paramObjectMap.latitude}, 2),
                        1 / 2
                )                                                                AS extent
         FROM store_welfare_payments_gathering swpg
                  LEFT JOIN
              `store_manage` sm
              ON swpg.store_manage_id = sm.id
        WHERE swpg.del_flag = 0
          AND sm.status = '1'
          AND sm.attestation_status IN (1, 2)
          AND sm.del_flag = '0'
          AND sm.store_type_id IS NOT NULL
        <if test="paramObjectMap.storeTypeId != null and paramObjectMap.storeTypeId != '' and paramObjectMap.hasChild == 2 and paramObjectMap.hasChild != ''">
            AND sm.store_type_id = #{paramObjectMap.storeTypeId}
        </if>
        <if test="paramObjectMap.storeTypeId != null and paramObjectMap.storeTypeId != '' and paramObjectMap.hasChild == 1 and paramObjectMap.hasChild != ''">
            AND sm.store_type_id IN (SELECT st.`id`
                                     FROM store_type st
                                     WHERE st.`del_flag` = '0'
                                       AND st.`status` = 1
                                       AND st.`pid` = #{paramObjectMap.storeTypeId})
        </if>
        ) m
        <if test="paramObjectMap.pattern == 0">
            ORDER BY m.extent ASC
        </if>
        <if test="paramObjectMap.pattern == 1">
            ORDER BY m.disCount DESC
        </if>
        <if test="paramObjectMap.pattern == 2">
            ORDER BY m.takeWayOne DESC,m.takeWayTwo DESC
        </if>
    </select>

    <select id="getLabelStoreManage" resultType="map">
        SELECT sm.id,
               IF(
                       LENGTH(TRIM(sm.`sub_store_name`)) &gt; 0,
                       CONCAT(
                               sm.`store_name`,
                               '(',
                               sm.`sub_store_name`,
                               ')'
                       ),
                       sm.`store_name`
               )                                    AS storeName,
               sm.logo_addr                         AS logoAddr,
               sm.store_address                     AS storeAddress,
               sm.comprehensive_evaluation          AS comprehensiveEvaluation,
               sm.longitude,
               sm.sys_user_id                       as sysUserId,
               sm.latitude,
               sm.status,
               sm.is_open_welfare_payments          AS isOpenWelfarePayments,
               sm.if_view_welfare_payments          AS ifViewWelfarePayments,
               (SELECT COUNT(1)
                FROM `marketing_discount`
                WHERE STATUS = 1
                  AND del_flag = '0'
                  AND total &gt;
                      released_quantity
                  AND sys_user_id = sm.sys_user_id) AS disCount
        FROM `store_manage` sm
                 LEFT JOIN store_label_relation slr ON slr.store_manage_id = sm.id
        WHERE sm.status = '1'
        <if test="paramMap.storeLabelId != null and paramMap.storeLabelId != ''">
            AND slr.store_label_id = #{paramMap.storeLabelId}
        </if>

        <if test="paramMap.floorName != null and paramMap.floorName != ''">
            AND sm.floor_name = #{paramMap.floorName}
        </if>
        AND sm.attestation_status IN (1, 2)
        AND sm.del_flag = '0'
        <if test="paramMap.discount == 1">
            ORDER BY disCount DESC
        </if>
        <if test="paramMap.discount != 1">
            ORDER BY sm.sort
        </if>
    </select>
    <select id="getPartnerSum" resultType="org.jeecg.modules.member.vo.MemberDesignationGroupVO">
        SELECT IF(
                       LENGTH(TRIM(sm.`sub_store_name`)) > 0,
                       CONCAT(
                               sm.`store_name`,
                               '(',
                               sm.`sub_store_name`,
                               ')'
                       ),
                       sm.`store_name`
               ) AS storeName,
               mdg.`total_members`,
               sm.id
        FROM member_designation_group mdg
                 LEFT JOIN store_manage sm
                           ON mdg.`store_manage_id` = sm.`id`
        WHERE sm.`alliance_user_id` = #{id}
        ORDER BY mdg.`total_members` DESC
    </select>

    <select id="getStoreList" resultType="map">
        SELECT sm.id,
               IF(
                       LENGTH(TRIM(sm.`sub_store_name`)) &gt; 0,
                       CONCAT(
                               sm.`store_name`,
                               '(',
                               sm.`sub_store_name`,
                               ')'
                       ),
                       sm.`store_name`
               )                AS storeName,
               sm.logo_addr     AS logoAddr,
               sm.store_address AS storeAddress,
               sm.sys_user_id   as sysUserId
        FROM `store_manage` sm
        WHERE sm.status = '1'
          AND sm.attestation_status IN (1, 2)
          AND sm.del_flag = '0'
        <if test="sysUserId != '' and sysUserId != null">
            AND sm.sys_user_id = #{sysUserId}
        </if>
    </select>

    <select id="getPrivilege" resultType="map">
        SELECT sm.id                                as storeId,
               IF
               (
                       LENGTH(
                               TRIM(sm.`sub_store_name`)) &gt; 0,
                       CONCAT(sm.`store_name`, '(', sm.`sub_store_name`, ')'),
                       sm.`store_name`
               )                                    AS storeName,
               sm.store_address                     AS storeAddress,
               sm.comprehensive_evaluation          AS comprehensiveEvaluation,
               sm.longitude,
               sm.latitude,
               sm.STATUS,
               sm.is_open_welfare_payments          AS isOpenWelfarePayments,
               sm.if_view_welfare_payments          AS ifViewWelfarePayments,
               sm.sys_user_id                       AS sysUserId,
               sm.logo_addr                         AS logoAddr,
               md.logo_addr                         AS mlogoAddr,
               (SELECT COUNT(1)
                FROM `marketing_discount`
                WHERE STATUS = 1
                  AND del_flag = '0'
                  AND total &gt;
                      released_quantity
                  AND sys_user_id = sm.sys_user_id) AS disCount,
               POWER(POWER(sm.longitude - #{paramMap.longitude}, 2) + POWER(sm.latitude - #{paramMap.latitude}, 2),
                     1 / 2
               )                                    AS extent,
               sm.sort,
               sm.area_address                      AS areaAddress,
               sm.take_out_phone                    AS takeOutPhone
        FROM member_designation_member_list mdml
                 LEFT JOIN member_designation_group mdg ON mdml.member_designation_group_id = mdg.id
                 LEFT JOIN store_manage sm ON sm.id = mdg.store_manage_id
                 LEFT JOIN member_designation md ON mdml.member_designation_id = md.id
        WHERE mdml.member_list_id = #{paramMap.memberId}
          AND mdml.del_flag = '0'
          and sm.id is not null
        ORDER BY mdml.create_time DESC
    </select>

    <select id="getPrivilegeInfo" resultType="map">
        SELECT sm.store_address            AS storeAddress,
               sm.comprehensive_evaluation AS comprehensiveEvaluation,
               sm.longitude,
               sm.latitude,
               sm.STATUS,
               sm.is_open_welfare_payments AS isOpenWelfarePayments,
               sm.if_view_welfare_payments AS ifViewWelfarePayments,
               sm.sort,
               sm.area_address             AS areaAddress,
               sm.take_out_phone           AS takeOutPhone,
               IF
               (
                       LENGTH(
                               TRIM(sm.`sub_store_name`)) &gt; 0,
                       CONCAT(sm.`store_name`, '(', sm.`sub_store_name`, ')'),
                       sm.`store_name`
               )                           AS storeName,
               sm.sys_user_id              AS sysUserId,
               sm.logo_addr                AS logoAddr,
               md.logo_addr                AS mlogoAddr,
               sm.id                       as storeId,
               (SELECT count(1)
                FROM marketing_store_gift_card_member_list
                WHERE member_list_id = mdml.member_list_id
                  AND `status` = '1'
                  AND del_flag = '0'
                  AND card_type = '0')     AS giftCardCount,
               (SELECT count(1)
                FROM marketing_certificate_record
                WHERE del_flag = '0'
                  AND member_list_id = mdml.member_list_id
                  AND `status` = '1')      AS certificateCount,
               (SELECT count(1)
                FROM marketing_discount_coupon
                WHERE del_flag = '0'
                  AND member_list_id = mdml.member_list_id
                  AND `status` = '1')      AS discountCount,
               POWER(POWER(sm.longitude - #{paramMap.longitude}, 2) + POWER(sm.latitude - #{paramMap.latitude}, 2),
                     1 / 2
               )                           AS extent
        FROM member_designation_member_list mdml
                 LEFT JOIN member_designation_group mdg ON mdml.member_designation_group_id = mdg.id
                 LEFT JOIN store_manage sm ON sm.id = mdg.store_manage_id
                 LEFT JOIN member_designation md ON mdml.member_designation_id = md.id
        WHERE mdml.member_list_id = #{paramMap.memberId}
          and sm.sys_user_id = #{paramMap.sysUserId}
          AND mdml.del_flag = '0'
        ORDER BY mdml.create_time DESC
        LIMIT 1
    </select>
    <select id="storeListAndNameNew" resultType="org.jeecg.modules.store.vo.StoreManageVO">
        select *
        from (select IF
                     (
                             LENGTH(
                                     TRIM(sm.`sub_store_name`)) > 0,
                             CONCAT(sm.`store_name`, '(', sm.`sub_store_name`, ')'),
                             sm.`store_name`
                     )              AS storeName,
                     sm.id,
                     sm.sys_user_id as sysUserId
              from store_manage sm
              where sm.del_flag = 0
                and sm.status = 1) m
        where 1 = 1
        <if test="storeName != null and storeName != ''">
            and m.storeName like concat('%', concat(#{storeName}, '%'))
        </if>
        <if test="sysUserId != null and sysUserId != ''">
            and m.sysUserId = #{sysUserId}
        </if>
        limit 30
    </select>

    <select id="sumTotalPerformance" resultType="java.math.BigDecimal">
        SELECT IFNULL(SUM(actually_received_amount), 0) AS total_performance
        FROM order_store_list
        WHERE del_flag = '0'
        AND status IN ('3', '5')
    </select>
</mapper>
