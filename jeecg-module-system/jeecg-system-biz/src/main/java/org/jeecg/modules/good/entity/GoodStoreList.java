package org.jeecg.modules.good.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecg.common.aspect.annotation.Dict;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;

/**
 * @Description: 店铺商品列表
 * @Author: jeecg-boot
 * @Date: 2019-10-25
 * @Version: V1.0
 */
@Data
@TableName("good_store_list")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "good_store_list对象", description = "店铺商品列表")
@JsonIgnoreProperties(value = {"hibernateLazyInitializer", "handler"})
public class GoodStoreList {

    /**
     * 主键ID
     */
    @TableId(type = IdType.ASSIGN_UUID)
    @ApiModelProperty(value = "主键ID")
    private String id;
    /**
     * 创建人
     */
    @Excel(name = "创建人", width = 15)
    @ApiModelProperty(value = "创建人")
    private String createBy;
    /**
     * 创建时间
     */
    @Excel(name = "创建时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private java.util.Date createTime;
    /**
     * 修改人
     */
    @Excel(name = "修改人", width = 15)
    @ApiModelProperty(value = "修改人")
    private String updateBy;
    /**
     * 修改时间
     */
    @Excel(name = "修改时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "修改时间")
    private java.util.Date updateTime;
    /**
     * 创建年
     */
    @Excel(name = "创建年", width = 15)
    @ApiModelProperty(value = "创建年")
    private Integer year;
    /**
     * 创建月
     */
    @Excel(name = "创建月", width = 15)
    @ApiModelProperty(value = "创建月")
    private Integer month;
    /**
     * 创建日
     */
    @Excel(name = "创建日", width = 15)
    @ApiModelProperty(value = "创建日")
    private Integer day;
    /**
     * 店铺用户id
     */
    @Excel(name = "店铺用户id", width = 15)
    @ApiModelProperty(value = "店铺用户id")
    private String sysUserId;
    /**
     * 删除状态（0，正常，1已删除）
     */
    @Excel(name = "删除状态（0，正常，1已删除）", width = 15)
    @ApiModelProperty(value = "删除状态（0，正常，1已删除）")
    @TableLogic
    private String delFlag;
    /**
     * 商品主图相对地址
     */
    @Excel(name = "商品主图相对地址", width = 15)
    @ApiModelProperty(value = "商品主图相对地址")
    private String mainPicture;
    /**
     * 商品名称
     */
    @Excel(name = "商品名称", width = 15)
    @ApiModelProperty(value = "商品名称")
    private String goodName;
    /**
     * 商品别名（默认与商品名称相同）
     */
    @Excel(name = "商品别名（默认与商品名称相同）", width = 15)
    @ApiModelProperty(value = "商品别名（默认与商品名称相同）")
    private String nickName;
    /**
     * 店铺商品分类id
     */
    @Excel(name = "店铺商品分类id", width = 15)
    @ApiModelProperty(value = "店铺商品分类id")
    private String goodStoreTypeId;
    /**
     * 商品编号
     */
    @Excel(name = "商品编号", width = 15)
    @ApiModelProperty(value = "商品编号")
    private String goodNo;
    /**
     * 运费模板
     */
    @Excel(name = "运费模板", width = 15)
    @ApiModelProperty(value = "运费模板")
    private String storeTemplateId;
    /**
     * 商品市场价
     */
    @Excel(name = "商品市场价", width = 15)
    @ApiModelProperty(value = "商品市场价")
    private String marketPrice;
    /**
     * 状态：0：停用；1：启用
     */
    @Excel(name = "状态：0：停用；1：启用", width = 15)
    @ApiModelProperty(value = "状态：0：停用；1：启用")
    private String status;
    /**
     * 商品描述
     */
    @Excel(name = "商品描述", width = 15)
    @ApiModelProperty(value = "商品描述")
    private String goodDescribe;
    /**
     * 商品视频地址
     */
    @Excel(name = "商品视频地址", width = 15)
    @ApiModelProperty(value = "商品视频地址")
    private String goodVideo;
    /**
     * 商品详情图多张以json的形式存储
     */
    @Excel(name = "商品详情图多张以json的形式存储", width = 15)
    @ApiModelProperty(value = "商品详情图多张以json的形式存储")
    private String detailsGoods;
    /**
     * 上下架；0：下架；1：上架
     */
    @Excel(name = "上下架；0：下架；1：上架", width = 15)
    @ApiModelProperty(value = "上下架；0：下架；1：上架")
    private String frameStatus;
    /**
     * 上下架说明
     */
    @Excel(name = "上下架说明", width = 15)
    @ApiModelProperty(value = "上下架说明")
    private String frameExplain;
    /**
     * 状态说明，停用说明
     */
    @Excel(name = "状态说明，停用说明", width = 15)
    @ApiModelProperty(value = "状态说明，停用说明")
    private String statusExplain;
    /**
     * 规格说明按照json的形式保存
     */
    @Excel(name = "规格说明按照json的形式保存", width = 15)
    @ApiModelProperty(value = "规格说明按照json的形式保存")
    private String specification;
    /**
     * 有无规格；0：无规格；1：有规格
     */
    @Excel(name = "有无规格；0：无规格；1：有规格", width = 15)
    @ApiModelProperty(value = "有无规格；0：无规格；1：有规格")
    private String isSpecification;
    /**
     * 服务承诺，来自数据字段逗号隔开
     */
    @Excel(name = "服务承诺，来自数据字段逗号隔开", width = 15)
    @ApiModelProperty(value = "服务承诺，来自数据字段逗号隔开")
    private String commitmentCustomers;
    /**
     * 删除原因
     */
    @Excel(name = "删除原因", width = 15)
    @ApiModelProperty(value = "删除原因")
    private String delExplain;
    /**
     * 删除时间
     */
    @Excel(name = "删除时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "删除时间")
    private java.util.Date delTime;
    /**
     * 自提状态：0：未启用；1：启用
     */
    @Excel(name = "自提状态：0：未启用；1：启用", width = 15)
    @ApiModelProperty(value = "自提状态：0：未启用；1：启用")
    private String noutoasiakasStatus;
    /**
     * 配送状态：0：未启用；1：启用
     */
    @Excel(name = "配送状态：0：未启用；1：启用", width = 15)
    @ApiModelProperty(value = "配送状态：0：未启用；1：启用")
    private String distributionStatus;
    /**
     * 审核状态：0:草稿；1：待审核；2：审核通过；3：审核不通过
     */
    @Excel(name = "审核状态：0:草稿；1：待审核；2：审核通过；3：审核不通过", width = 15)
    @ApiModelProperty(value = "审核状态：0:草稿；1：正常；")
    private String auditStatus;
    /**
     * 审核原因
     */
    @Excel(name = "审核原因", width = 15)
    @ApiModelProperty(value = "审核原因")
    private String auditExplain;

    private String specifications;


    private String searchInfo;

    /**
     * 配送方式；0：快递；1：自提；2：配送；数据字典：oder_distribution
     */
    private String distribution;

    /**
     * 排序
     */
    private BigDecimal sort;


//    ***********************批发栏目、甄品优选栏目新增字段 --- by 张少林 20230901

    /**
     * 是否产品批发 0=否 1=是
     */
    private String isWholesale;

    /**
     * 是否甄品优选（0=否 1=是）
     */
    private String isSelectedProducts;

    /**
     * 店铺分佣比例（产品批发）
     */
    private BigDecimal productStoreCommissionRate;
    /**
     * 产品批发：本人分佣比例
     */
    private BigDecimal productAgencyCommissionRateMyself;
    /**
     * 产品批发：一级代理商分佣比例
     */
    private BigDecimal productAgencyCommissionRate;
    /**
     * 产品批发：二级代理商分佣比例
     */
    private BigDecimal productAgencyCommissionRateTwo;

    /**
     * 商品分享佣金比例
     */
    private BigDecimal shareCommissionRate;

    /**
     * 店铺分佣比例（甄选优品）
     */
    private BigDecimal selectedStoreCommissionRate;

    /**
     * 备注
     */
    private String remarks;

    @Excel(name = "分享图", width = 15)
    @ApiModelProperty(value = "分享图")
    private String sharePicture;

    /**店铺类型；15：助农订单；16：助残订单；17：助大学生再就业订单*/
    @Excel(name = "店铺类型；15：助农订单；16：助残订单；17：助大学生再就业订单", width = 15)
    @ApiModelProperty(value = "店铺类型；15：助农订单；16：助残订单；17：助大学生再就业订单")
    @Dict(dicCode = "store_type")
    private String storeType;

    /**
     * 累计业绩
     */
    private BigDecimal totalPerformance;

    /**
     * 库存
     */
    @Excel(name = "库存", width = 15)
    @ApiModelProperty(value = "库存")
    private BigDecimal repertory;

    /**
     * 是否曝光商品：0=否，1=是
     */
    @Excel(name = "是否曝光商品", width = 15)
    @ApiModelProperty(value = "是否曝光商品：0=否，1=是")
    @Dict(dicCode = "exposure_good_status")
    private String isExposureGood;

    /**
     * 曝光排序值，数值越小优先级越高
     */
    @Excel(name = "曝光排序值", width = 15)
    @ApiModelProperty(value = "曝光排序值，数值越小优先级越高，默认999")
    @TableField("exposure_sort_value")
    private Integer exposureSortValue;

    /**
     * 打榜曝光业绩
     */
    @Excel(name = "打榜曝光业绩", width = 15)
    @ApiModelProperty(value = "打榜曝光业绩")
    private BigDecimal exposurePerformance;
}
