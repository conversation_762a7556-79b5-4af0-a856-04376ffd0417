package org.jeecg.modules.good.controller;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import com.alibaba.druid.support.ibatis.SqlMapClientImplWrapper;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.aspect.annotation.PermissionData;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.util.OrderNoUtils;
import org.jeecg.common.util.SpringContextUtils;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.config.util.PermissionUtils;
import org.jeecg.modules.good.dto.GoodStoreDiscountDTO;
import org.jeecg.modules.good.dto.GoodStoreListDto;
import org.jeecg.modules.good.dto.GoodStoreListNewDTO;
import org.jeecg.modules.good.dto.GoodStoreListProDTO;
import org.jeecg.modules.good.entity.GoodList;
import org.jeecg.modules.good.entity.GoodStoreList;
import org.jeecg.modules.good.entity.GoodStoreSpecification;
import org.jeecg.modules.good.exception.GoodSpecificationValidationException;
import org.jeecg.modules.good.service.IGoodStoreListService;
import org.jeecg.modules.good.service.IGoodStoreSpecificationService;
import org.jeecg.modules.good.service.IGoodStoreTypeService;
import org.jeecg.modules.good.util.GoodUtils;
import org.jeecg.modules.good.vo.GoodStoreListVo;
import org.jeecg.modules.store.entity.StoreManage;
import org.jeecg.modules.store.entity.StoreTemplate;
import org.jeecg.modules.store.service.IStoreManageService;
import org.jeecg.modules.store.service.IStoreTemplateService;
import org.jeecg.modules.system.service.ISysUserRoleService;
import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.net.URLDecoder;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @Description: 店铺商品列表
 * @Author: jeecg-boot
 * @Date: 2019-10-25
 * @Version: V1.0
 */
@Slf4j
@Api(tags = "店铺商品列表")
@RestController
@RequestMapping("/goodStoreList/goodStoreList")
public class GoodStoreListController {
    @Autowired
    private IGoodStoreListService goodStoreListService;

    @Autowired
    private IGoodStoreSpecificationService iGoodStoreSpecificationService;

    @Autowired
    private IStoreManageService iStoreManageService;

    @Autowired
    private IGoodStoreTypeService iGoodStoreTypeService;


    @Autowired
    private GoodUtils goodUtils;

    @Autowired
    private IStoreTemplateService storeTemplateService;

    /**
     * 修改商品排序
     *
     * @param goodListId
     * @param sort
     * @return
     */
    @RequestMapping("updateSort")
    @ResponseBody
    public Result<?> updateSort(String goodListId,
                                @RequestParam(required = false, defaultValue = "0") BigDecimal sort) {
        if (StringUtils.isBlank(goodListId)) {
            return Result.error("商品id不能为空");
        }
        GoodStoreList goodStoreList = goodStoreListService.getById(goodListId);
        goodStoreList.setSort(sort);
        goodStoreListService.updateById(goodStoreList);
        return Result.ok("修改排序成功！！！");
    }


    /**
     * 商品选择组件
     *
     * @param goodName
     * @param goodTypeId
     * @param pageNo
     * @param pageSize
     * @return
     */
    @GetMapping("selectGood")
    public Result<?> selectGood(@RequestParam(defaultValue = "", name = "goodName", required = false) String goodName,
                                @RequestParam(defaultValue = "", name = "goodTypeId", required = false) String goodTypeId,
                                String storeManageId,
                                @RequestParam(name = "isWholesale", defaultValue = "0", required = false) String isWholesale,
                                @RequestParam(name = "isSelectedProducts", defaultValue = "0", required = false) String isSelectedProducts,
                                @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                @RequestParam(name = "pageSize", defaultValue = "5") Integer pageSize) {
        Map<String, Object> paramMap = Maps.newHashMap();
        paramMap.put("goodName", goodName);
        paramMap.put("goodTypeId", goodTypeId);
        paramMap.put("sysUserId", iStoreManageService.getById(storeManageId).getSysUserId());
        paramMap.put("isWholesale",isWholesale);
        paramMap.put("isSelectedProducts",isSelectedProducts);
        log.info("查询条件：" + JSON.toJSONString(paramMap));
        IPage<Map<String, Object>> iPage = goodStoreListService.selectGood(new Page<>(pageNo, pageSize), paramMap);
        iPage.getRecords().forEach(g -> {
            g.put("storeSpecifications", iGoodStoreSpecificationService.listMaps(new QueryWrapper<GoodStoreSpecification>()
                    .select("specification", "price", "repertory")
                    .lambda()
                    .eq(GoodStoreSpecification::getGoodStoreListId, g.get("id"))));
        });
        return Result.ok(iPage);
    }


    /**
     * 分页列表查询
     *
     * @param goodList
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @AutoLog(value = "店铺商品列表-分页列表查询")
    @ApiOperation(value = "店铺商品列表-分页列表查询", notes = "店铺商品列表-分页列表查询")
    @GetMapping(value = "/listNew")
    public Result<?> queryPageListNew(GoodStoreList goodList,
                                      @RequestParam(name = "level", defaultValue = "-1", required = false) String level,
                                      @RequestParam(name = "typeId", defaultValue = "", required = false) String typeId,
                                      @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                      @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                      HttpServletRequest req) {
        String sysUserId = PermissionUtils.ifStore();
        IPage<Map<String, Object>> pageList = goodStoreListService.queryPageListNew(goodList, sysUserId, level, typeId, pageNo, pageSize, req);
        return Result.ok(pageList);
    }


    /**
     * 分页列表查询
     *
     * @param goodStoreListVo
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @AutoLog(value = "店铺商品列表-分页列表查询")
    @ApiOperation(value = "店铺商品列表-分页列表查询", notes = "店铺商品列表-分页列表查询")
    @GetMapping(value = "/list")
    public Result<IPage<GoodStoreListDto>> queryPageList(GoodStoreListVo goodStoreListVo,
                                                         @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                         @RequestParam(name = "pageSize", defaultValue = "5") Integer pageSize,
                                                         @RequestParam(name = "isWholesale", defaultValue = "0", required = false) String isWholesale,
                                                         @RequestParam(name = "isSelectedProducts", defaultValue = "0", required = false) String isSelectedProducts,
                                                         @RequestParam(name = "column", required = false) String column,
                                                         @RequestParam(name = "order", required = false) String order,
                                                         HttpServletRequest req) {
        Result<IPage<GoodStoreListDto>> result = new Result<IPage<GoodStoreListDto>>();
        Page<GoodStoreList> page = new Page<GoodStoreList>(pageNo, pageSize);

        // 处理排序参数
//        if (StringUtils.isNotBlank(column) && StringUtils.isNotBlank(order)) {
//            // 字段名映射：前端字段名 -> 数据库字段名
//            String dbColumn = column;
//            if ("exposureSortValue".equals(column)) {
//                dbColumn = "exposure_sort_value";
//            }
//
//            // 设置排序
//            if ("asc".equalsIgnoreCase(order)) {
//                page.addOrder(OrderItem.asc(dbColumn));
//            } else if ("desc".equalsIgnoreCase(order)) {
//                page.addOrder(OrderItem.desc(dbColumn));
//            }
//        }

        //判断当前用户是否是品台，是 STR=NULL 不是STR= UserId
        String str = PermissionUtils.ifPlatform();
        if (StringUtils.isNotBlank(str)) {
            goodStoreListVo.setSysUserId(str);
        }
        //goodStoreListVo.setAuditStatus("1");
        IPage<GoodStoreListDto> pageList = goodStoreListService.getGoodListDto(page, goodStoreListVo, "0");
        result.setSuccess(true);
        result.setResult(pageList);
        return result;

    }

    @AutoLog(value = "店铺商品列表-分页列表查询")
    @ApiOperation(value = "店铺商品列表-分页列表查询", notes = "店铺商品列表-分页列表查询")
    @GetMapping(value = "/findStoreList")
    public Result<List<GoodStoreDiscountDTO>> findStoreList(GoodStoreListVo goodStoreListVo) {
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        ISysUserRoleService iSysUserRoleService = SpringContextUtils.getBean(ISysUserRoleService.class);
        List<String> roleByUserId = iSysUserRoleService.getRoleByUserId(sysUser.getId());
        if (roleByUserId.contains("Merchant")) {
            goodStoreListVo.setSysUserId(sysUser.getId());
        }
        Result<List<GoodStoreDiscountDTO>> result = new Result<List<GoodStoreDiscountDTO>>();
        List<GoodStoreDiscountDTO> goodById = goodStoreListService.findStoreGoodList(goodStoreListVo);
        result.setSuccess(true);
        result.setResult(goodById);
        return result;
    }

    /**
     * 分页草稿箱列表查询
     *
     * @param goodStoreListVo
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @AutoLog(value = "商品列表-分页列表查询")
    @ApiOperation(value = "商品列表-分页列表查询", notes = "商品列表-分页列表查询")
    @RequestMapping(value = "/GoodStoreDraftListList", method = RequestMethod.GET)
    @PermissionData(pageComponent = "good/GoodStoreDraftListList")
    public Result<IPage<GoodStoreListDto>> queryPageDraftList(GoodStoreListVo goodStoreListVo,
                                                              @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                              @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                                              HttpServletRequest req) {
        Result<IPage<GoodStoreListDto>> result = new Result<IPage<GoodStoreListDto>>();
        Page<GoodStoreList> page = new Page<GoodStoreList>(pageNo, pageSize);
        //判断当前用户是否是品台，是 STR=NULL 不是STR= UserId
        String str = PermissionUtils.ifPlatform();
        if (StringUtils.isNotBlank(str)) {
            goodStoreListVo.setSysUserId(str);
        }
        goodStoreListVo.setAuditStatus("0");
        IPage<GoodStoreListDto> pageList = goodStoreListService.getGoodListDto(page, goodStoreListVo, null);
        result.setSuccess(true);
        result.setResult(pageList);
        return result;
    }

    /**
     * 分页回收站列表查询
     *
     * @param goodStoreListVo
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @AutoLog(value = "商品列表-分页列表查询")
    @ApiOperation(value = "商品列表-分页列表查询", notes = "商品列表-分页列表查询")
    @GetMapping(value = "/GoodStoreListRecycleList")
    @PermissionData(pageComponent = "good/GoodStoreListRecycleList")
    public Result<IPage<GoodStoreListDto>> queryPageRecycleList(GoodStoreListVo goodStoreListVo,
                                                                @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                                @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                                                HttpServletRequest req) {
        Result<IPage<GoodStoreListDto>> result = new Result<IPage<GoodStoreListDto>>();
        Page<GoodStoreList> page = new Page<GoodStoreList>(pageNo, pageSize);
        //判断当前用户是否是品台，是 STR=NULL 不是STR= UserId
        String str = PermissionUtils.ifPlatform();
        if (StringUtils.isNotBlank(str)) {
            goodStoreListVo.setSysUserId(str);
        }
        goodStoreListVo.setDelFlag("1");
        IPage<GoodStoreListDto> pageList = goodStoreListService.getGoodListDtoDelFlag(page, goodStoreListVo);
        result.setSuccess(true);
        result.setResult(pageList);
        return result;
    }

    /**
     * 分页待审核列表查询
     *
     * @param goodStoreListVo
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    @AutoLog(value = "商品列表-分页列表查询")
    @ApiOperation(value = "商品列表-分页列表查询", notes = "商品列表-分页列表查询")
    @GetMapping(value = "/GoodStoreAuditListList")
    @PermissionData(pageComponent = "good/GoodStoreAuditListList")
    public Result<IPage<GoodStoreListDto>> queryPageAuditList(GoodStoreListVo goodStoreListVo,
                                                              @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                              @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                                              HttpServletRequest req) {
        Result<IPage<GoodStoreListDto>> result = new Result<IPage<GoodStoreListDto>>();
        Page<GoodStoreList> page = new Page<GoodStoreList>(pageNo, pageSize);

        goodStoreListVo.setAuditStatus("1");
        //判断当前用户是否是品台，是 STR=NULL 不是STR= UserId
        String str = PermissionUtils.ifPlatform();
        if (str != null) {
            goodStoreListVo.setSysUserId(str);
        }
        IPage<GoodStoreListDto> pageList = goodStoreListService.getGoodListDto(page, goodStoreListVo, "0");
        result.setSuccess(true);
        result.setResult(pageList);
        return result;
    }


    /**
     * 添加
     *
     * @param goodStoreListNewDTO
     * @return
     */
    @AutoLog(value = "店铺商品列表-添加")
    @ApiOperation(value = "店铺商品列表-添加", notes = "店铺商品列表-添加")
    @PostMapping(value = "/add")
    public Result<?> add(@RequestBody GoodStoreListNewDTO goodStoreListNewDTO) {
        log.info(JSON.toJSONString(goodStoreListNewDTO));
        //商品分类
        if (StringUtils.isBlank(goodStoreListNewDTO.getGoodTypeId())) {
            return Result.error("请选择商品分类");
        }
        //商品名称
        if (StringUtils.isBlank(goodStoreListNewDTO.getGoodName())) {
            return Result.error("商品名称不能为空");
        }
        //店铺
        if (StringUtils.isBlank(goodStoreListNewDTO.getStoreManageId())) {
            return Result.error("请选择店铺");
        }
        StoreManage storeManage=iStoreManageService.getById(goodStoreListNewDTO.getStoreManageId());
        if (StringUtils.isNotBlank(goodStoreListNewDTO.getGoodNo()) && goodStoreListService.count(new LambdaQueryWrapper<GoodStoreList>().eq(GoodStoreList::getSysUserId, storeManage.getSysUserId()).eq(GoodStoreList::getGoodNo, goodStoreListNewDTO.getGoodNo())) > 0) {
            return Result.error("商品编号不能重复，请重新编写");
        }
        //市场价
        if (goodStoreListNewDTO.getMarketPrice() == null || goodStoreListNewDTO.getMarketPrice().doubleValue() == 0) {
            return Result.error("请填写市场价或者市场价必须高于0");
        }

        //请选择运费模板
        if (StringUtils.contains(goodStoreListNewDTO.getDistribution(), "0") && StringUtils.isBlank(goodStoreListNewDTO.getStoreTemplateId())) {
            return Result.error("请选择运费模板");
        }

        goodStoreListNewDTO.setSysUserId(storeManage.getSysUserId());
        boolean b = goodStoreListService.add(goodStoreListNewDTO);

        if (b) {
            return Result.ok("添加成功！");
        } else {
            return Result.error("未知错误，请联系管理员");
        }

    }


    /**
     * 添加商品-- 支持两级规格类型
     *
     * @param goodStoreListNewDTO
     * @return
     */
    @AutoLog(value = "店铺商品列表-添加")
    @ApiOperation(value = "店铺商品列表-添加", notes = "店铺商品列表-添加")
    @PostMapping(value = "/addNew")
    public Result<?> addNew(@RequestBody GoodStoreListVo goodStoreListNewDTO) {
        log.info(JSON.toJSONString(goodStoreListNewDTO));
        //商品分类
        if (StringUtils.isBlank(goodStoreListNewDTO.getGoodStoreTypeId())) {
            return Result.error("请选择商品分类");
        }
        //商品名称
        if (StringUtils.isBlank(goodStoreListNewDTO.getGoodName())) {
            return Result.error("商品名称不能为空");
        }
        if (StrUtil.isBlank(goodStoreListNewDTO.getGoodNo())) {
            goodStoreListNewDTO.setGoodNo(OrderNoUtils.getOrderNo());
        }
        //店铺
        if (StringUtils.isBlank(goodStoreListNewDTO.getSysUserId())) {
            return Result.error("请选择店铺");
        }
        StoreManage storeManage=iStoreManageService.getStoreManageBySysUserId(goodStoreListNewDTO.getSysUserId());

        if (StringUtils.isNotBlank(goodStoreListNewDTO.getGoodNo()) && goodStoreListService.count(new LambdaQueryWrapper<GoodStoreList>()
//                .eq(GoodStoreList::getSysUserId, storeManage.getSysUserId())
                .eq(GoodStoreList::getGoodNo, goodStoreListNewDTO.getGoodNo())
                .ne(StrUtil.isNotBlank(goodStoreListNewDTO.getId()),GoodStoreList::getId, goodStoreListNewDTO.getId())) > 0) {
            return Result.error("商品编号不能重复，请重新编写");
        }
        //市场价
        if (goodStoreListNewDTO.getMarketPrice() == null || Convert.toBigDecimal(goodStoreListNewDTO.getMarketPrice(),BigDecimal.ZERO).compareTo(BigDecimal.ZERO) == 0) {
            return Result.error("请填写市场价或者市场价必须高于0");
        }
        goodStoreListNewDTO.setStoreTemplateId(goodStoreListNewDTO.getProviderTemplateId());

        if (StrUtil.isBlank(goodStoreListNewDTO.getStoreTemplateId())) {
            //系统默认运费模板
            LambdaQueryWrapper<StoreTemplate> storeTemplateLambdaQueryWrapper = new LambdaQueryWrapper<>();
            storeTemplateLambdaQueryWrapper
                    .eq(StoreTemplate::getSysUserId, storeManage.getSysUserId())
                    .eq(StoreTemplate::getIsTemplate, 1)
                    .orderByDesc(StoreTemplate::getCreateTime)
                    .last("limit 1");
            StoreTemplate storeTemplate = storeTemplateService.getOne(storeTemplateLambdaQueryWrapper, false);
            if (storeTemplate != null) {
                goodStoreListNewDTO.setStoreTemplateId(storeTemplate.getId());
            }
        }

        //请选择运费模板
//        if (StringUtils.contains(goodStoreListNewDTO.getDistribution(), "0") && StringUtils.isBlank(goodStoreListNewDTO.getStoreTemplateId())) {
//            return Result.error("请选择运费模板");
//        }

        goodStoreListNewDTO.setSysUserId(storeManage.getSysUserId());
        goodStoreListNewDTO.setStoreType(storeManage.getStoreType());

        try {
            boolean b = this.goodStoreListService.saveOrUpdate(goodStoreListNewDTO);

            if (b) {
                return Result.ok("添加成功！");
            } else {
                return Result.error("未知错误，请联系管理员");
            }
        } catch (GoodSpecificationValidationException e) {
            log.error("商品规格数据校验失败", e);

            // 返回详细的校验错误信息
            String errorMessage = e.getFirstErrorMessage();
            if (e.hasWarnings()) {
                errorMessage += "；警告：" + e.getAllWarningMessages();
            }

            return Result.error(errorMessage);
        } catch (Exception e) {
            log.error("保存商品数据异常", e);
            return Result.error("保存失败：" + e.getMessage());
        }

    }

    /**
     * 编辑
     *
     * @param goodStoreListNewDTO
     * @return
     */
    @AutoLog(value = "店铺商品列表-编辑")
    @ApiOperation(value = "店铺商品列表-编辑", notes = "店铺商品列表-编辑")
    @PutMapping(value = "/edit")
    public Result<?> edit(@RequestBody GoodStoreListNewDTO goodStoreListNewDTO) {
        log.info(JSON.toJSONString(goodStoreListNewDTO));
        //店铺
        if (StringUtils.isBlank(goodStoreListNewDTO.getStoreManageId())) {
            return Result.error("请选择店铺");
        }
        StoreManage storeManage = iStoreManageService.getById(goodStoreListNewDTO.getStoreManageId());
        //请选择运费模板
        if (StringUtils.contains(goodStoreListNewDTO.getDistribution(), "0") && StringUtils.isBlank(goodStoreListNewDTO.getStoreTemplateId())) {
            return Result.error("请选择运费模板");
        }
        //市场价
        if (goodStoreListNewDTO.getMarketPrice() == null || goodStoreListNewDTO.getMarketPrice().doubleValue() == 0) {
            return Result.error("请填写市场价或者市场价必须高于0");
        }
        if (StringUtils.isNotBlank(goodStoreListNewDTO.getGoodNo()) && goodStoreListService.count(new LambdaQueryWrapper<GoodStoreList>().eq(GoodStoreList::getGoodNo, goodStoreListNewDTO.getGoodNo()).eq(GoodStoreList::getSysUserId, storeManage.getSysUserId()).ne(GoodStoreList::getId, goodStoreListNewDTO.getId())) > 0) {
            return Result.error("商品编号不能重复，请重新编写");
        }
        //商品名称
        if (StringUtils.isBlank(goodStoreListNewDTO.getGoodName())) {
            return Result.error("商品名称不能为空");
        }
        //商品分类
        if (StringUtils.isBlank(goodStoreListNewDTO.getGoodTypeId())) {
            return Result.error("请选择商品分类");
        }
        if (StringUtils.isBlank(goodStoreListNewDTO.getId())){
            return Result.error("商品id未传递");
        }
        GoodStoreList goodList = goodStoreListService.getById(goodStoreListNewDTO.getId());
        goodList.setSysUserId(storeManage.getSysUserId());

        //保存数据
        if (goodStoreListService.edit(goodStoreListNewDTO,goodList)) {
            return Result.ok("编辑成功!");
        } else {
            return Result.error("未知错误，请联系管理员");
        }

    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "店铺商品列表-通过id删除")
    @ApiOperation(value = "店铺商品列表-通过id删除", notes = "店铺商品列表-通过id删除")
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name = "id", required = true) String id) {
        try {
            goodStoreListService.removeById(id);
        } catch (Exception e) {
            log.error("删除失败", e.getMessage());
            return Result.error("删除失败!");
        }
        return Result.ok("删除成功!");
    }

    /**
     * 批量id查询修改删除状态：
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "商品管理-通过id查询")
    @ApiOperation(value = "商品管理-通过id查询", notes = "商品管理-通过id查询")
    @GetMapping(value = "/updateDelFlags")
    public Result<GoodStoreList> updateDelFlags(@RequestParam(name = "ids", required = true) String ids) {
        Result<GoodStoreList> result = new Result<GoodStoreList>();
        if (ids == null || "".equals(ids.trim())) {
            result.error500("参数不识别！");
        } else {
            GoodStoreList goodStoreList;
            try {
                List<String> listid = Arrays.asList(ids.split(","));
                //goodList.setDelFlag("0");
                for (String id : listid) {
                    goodStoreList = goodStoreListService.getGoodStoreListById(id);
                    if (goodStoreList == null) {
                        result.error500("未找到对应实体");
                    } else {
                        goodStoreListService.updateDelFalg(goodStoreList, "0");
                    }
                }
                result.success("修改成功!");
            } catch (Exception e) {
                result.error500("修改失败！");
            }
        }
        return result;
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "店铺商品列表-批量删除")
    @ApiOperation(value = "店铺商品列表-批量删除", notes = "店铺商品列表-批量删除")
    @DeleteMapping(value = "/deleteBatch")
    public Result<GoodStoreList> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        Result<GoodStoreList> result = new Result<GoodStoreList>();
        if (ids == null || "".equals(ids.trim())) {
            result.error500("参数不识别！");
        } else {
            this.goodStoreListService.removeByIds(Arrays.asList(ids.split(",")));
            result.success("删除成功!");
        }
        return result;
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    @AutoLog(value = "店铺商品列表-通过id查询")
    @ApiOperation(value = "店铺商品列表-通过id查询", notes = "店铺商品列表-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<GoodStoreList> queryById(@RequestParam(name = "id", required = true) String id) {
        Result<GoodStoreList> result = new Result<GoodStoreList>();
        GoodStoreList goodStoreList = goodStoreListService.getById(id);
        if (goodStoreList == null) {
            result.error500("未找到对应实体");
        } else {
            result.setResult(goodStoreList);
            result.setSuccess(true);
        }
        return result;
    }

    /**
     * 导出excel
     *
     * @param request
     * @param response
     */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, HttpServletResponse response) {
        // Step.1 组装查询条件
        QueryWrapper<GoodStoreList> queryWrapper = null;
        try {
            String paramsStr = request.getParameter("paramsStr");
            if (oConvertUtils.isNotEmpty(paramsStr)) {
                String deString = URLDecoder.decode(paramsStr, "UTF-8");
                GoodStoreList goodStoreList = JSON.parseObject(deString, GoodStoreList.class);
                queryWrapper = QueryGenerator.initQueryWrapper(goodStoreList, request.getParameterMap());
            }
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }

        //Step.2 AutoPoi 导出Excel
        ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
        List<GoodStoreList> pageList = goodStoreListService.list(queryWrapper);
        //导出文件名称
        mv.addObject(NormalExcelConstants.FILE_NAME, "店铺商品列表列表");
        mv.addObject(NormalExcelConstants.CLASS, GoodStoreList.class);
        mv.addObject(NormalExcelConstants.PARAMS, new ExportParams("店铺商品列表列表数据", "导出人:Jeecg", "导出信息"));
        mv.addObject(NormalExcelConstants.DATA_LIST, pageList);
        return mv;
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
        Map<String, MultipartFile> fileMap = multipartRequest.getFileMap();
        for (Map.Entry<String, MultipartFile> entity : fileMap.entrySet()) {
            MultipartFile file = entity.getValue();// 获取上传文件对象
            ImportParams params = new ImportParams();
            params.setTitleRows(2);
            params.setHeadRows(1);
            params.setNeedSave(true);
            try {
                List<GoodStoreList> listGoodStoreLists = ExcelImportUtil.importExcel(file.getInputStream(), GoodStoreList.class, params);
                goodStoreListService.saveBatch(listGoodStoreLists);
                return Result.ok("文件导入成功！数据行数:" + listGoodStoreLists.size());
            } catch (Exception e) {
                log.error(e.getMessage(), e);
                return Result.error("文件导入失败:" + e.getMessage());
            } finally {
                try {
                    file.getInputStream().close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return Result.ok("文件导入失败！");
    }


    /**
     * 通过id查询修改审核状态：
     *
     * @param id
     * @return
     */
    @AutoLog(value = "商品管理-通过id查询")
    @ApiOperation(value = "商品管理-通过id查询", notes = "广告管理-通过id查询")
    @GetMapping(value = "/updateAuditStatus")
    public Result<GoodStoreList> updateAuditStatus(@RequestParam(name = "id", required = true) String id, @RequestParam(name = "auditStatus") String auditStatus, String auditExplain) {
        Result<GoodStoreList> result = new Result<GoodStoreList>();
        GoodStoreList goodStoreList = goodStoreListService.getById(id);
        if (goodStoreList == null) {
            result.error500("未找到对应实体");
        } else {
            goodStoreList.setAuditExplain(auditExplain);
            goodStoreList.setAuditStatus(auditStatus);
            boolean ok = goodStoreListService.updateById(goodStoreList);
            //TODO 返回false说明什么？
            if (ok) {
                result.success("修改成功!");
            } else {
                result.error500("修改失败！");
            }
        }
        return result;
    }

    /**
     * 通过id查询修改删除状态：
     *
     * @param id
     * @return
     */
    @AutoLog(value = "商品管理-通过id查询")
    @ApiOperation(value = "商品管理-通过id查询", notes = "商品管理-通过id查询")
    @GetMapping(value = "/updateDelFlag")
    public Result<GoodStoreList> updateDelFlag(@RequestParam(name = "id", required = true) String id, String delExplain) {
        Result<GoodStoreList> result = new Result<GoodStoreList>();
        GoodStoreList goodList = goodStoreListService.getGoodStoreListById(id);
        if (goodList == null) {
            result.error500("未找到对应实体");
        } else {
            try {
                goodList.setDelExplain(delExplain);
                goodList.setDelTime(new Date());
                //修改
                goodStoreListService.updateById(goodList);
                //删除
                goodStoreListService.removeById(goodList.getId());
                result.success("修改成功!");
            } catch (Exception e) {
                result.error500("修改失败！");
            }
        }
        return result;
    }

    /**
     * 批量设置曝光商品状态
     *
     * @param ids
     * @param isExposureGood
     * @return
     */
    @AutoLog(value = "商品管理-批量设置曝光状态")
    @ApiOperation(value = "商品管理-批量设置曝光状态", notes = "商品管理-批量设置曝光状态")
    @PostMapping(value = "/batchUpdateExposureStatus")
    public Result<String> batchUpdateExposureStatus(@RequestParam(name = "ids", required = true) String ids,
                                                    @RequestParam(name = "isExposureGood", required = true) String isExposureGood) {
        Result<String> result = new Result<String>();

        // 参数验证
        if (ids == null || "".equals(ids.trim())) {
            return Result.error("商品ID参数不能为空");
        }

        if (!"0".equals(isExposureGood) && !"1".equals(isExposureGood)) {
            return Result.error("曝光状态参数无效，只能是0或1");
        }

        try {
            List<String> idList = Arrays.asList(ids.split(","));
            int successCount = 0;
            int failCount = 0;

            for (String id : idList) {
                GoodStoreList goodStoreList = goodStoreListService.getById(id);
                if (goodStoreList == null) {
                    log.warn("商品不存在，ID: {}", id);
                    failCount++;
                    continue;
                }

                // 更新曝光状态
                goodStoreList.setIsExposureGood(isExposureGood);
                goodStoreListService.updateById(goodStoreList);
                successCount++;

                log.info("批量设置曝光状态成功，商品ID: {}, 曝光状态: {}", id, isExposureGood);
            }

            String message = String.format("批量操作完成！成功: %d 条，失败: %d 条", successCount, failCount);
            result.setSuccess(true);
            result.setMessage(message);
            result.setResult(message);

        } catch (Exception e) {
            log.error("批量设置曝光状态失败", e);
            return Result.error("批量设置曝光状态失败：" + e.getMessage());
        }

        return result;
    }

    /**
     * 批量修改上机架
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "商品管理-通过id查询")
    @ApiOperation(value = "商品管理-通过id查询", notes = "商品管理-通过id查询")
    @GetMapping(value = "/updateFrameStatus")
    public Result<GoodStoreList> updateFrameStatus(@RequestParam(name = "ids", required = true) String ids,
                                                   @RequestParam(name = "frameStatus", required = true) String frameStatus,
                                                   String frameExplain) {
        Result<GoodStoreList> result = new Result<GoodStoreList>();
        if (ids == null || "".equals(ids.trim())) {
            result.error500("参数不识别！");
        } else {
            GoodStoreList goodStoreList;
            try {
                List<String> listid = Arrays.asList(ids.split(","));
                for (String id : listid) {
                    goodStoreList = goodStoreListService.getGoodStoreListById(id);
                    if (goodStoreList == null) {
                        result.error500("未找到对应实体");
                    } else {
                        goodStoreList.setFrameExplain(frameExplain);
                        goodStoreList.setFrameStatus(frameStatus);
                        goodStoreListService.updateById(goodStoreList);
                    }
                }
                result.success("修改成功!");
            } catch (Exception e) {
                result.error500("修改失败！");
            }
        }
        return result;
    }

    /**
     * 通过id查询修改审核状态：
     * @param
     * @return
     */
    /**
     * @param json ID +AuditStatus
     * @return
     */
    @AutoLog(value = "商品管理-通过id查询")
    @ApiOperation(value = "商品管理-通过id查询", notes = "商品管理-通过id查询")
    // @GetMapping(value = "/updateAuditStatus")
    @RequestMapping(value = "/updateAuditStatusPL", method = RequestMethod.PUT)
    public Result<GoodStoreList> updateAuditStatusPL(@RequestBody JSONObject json) {
        Result<GoodStoreList> result = new Result<GoodStoreList>();
        String ids = json.getString("id");
        String auditStatus = json.getString("auditStatus");
        String auditExplain = json.getString("auditExplain");
        if (StringUtils.isBlank(auditStatus)) {
            result.error500("审核状态不能为空！");
            return result;
        }
        if (StringUtils.isEmpty(ids)) {
            result.error500("用户id不能为空！");
            return result;
        }
        GoodStoreList goodStoreList;
        try {
            List<String> listid = Arrays.asList(ids.split(","));
            for (String id : listid) {
                goodStoreList = goodStoreListService.getById(id);
                if (goodStoreList == null) {
                    result.error500("未找到对应实体");
                } else {

                    goodStoreList.setAuditStatus(auditStatus);
                    goodStoreList.setAuditExplain(auditExplain);
                    goodStoreListService.updateById(goodStoreList);
                    result.success("修改成功!");

                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            result.error500("修改失败！");
        }
        return result;
    }

    /**
     * 通过id查询修改状态:启用停用
     *
     * @param id
     * @return
     */
    @AutoLog(value = "商品管理-通过id查询")
    @ApiOperation(value = "商品管理-通过id查询", notes = "商品管理-通过id查询")
    @GetMapping(value = "/updateStatus")
    public Result<GoodStoreList> updateStatus(@RequestParam(name = "id", required = true) String id, @RequestParam(name = "statusExplain") String statusExplain) {
        Result<GoodStoreList> result = new Result<GoodStoreList>();
        GoodStoreList goodStoreList = goodStoreListService.getById(id);
        if (goodStoreList == null) {
            result.error500("未找到对应实体");
        } else {
            if ("1".equals(goodStoreList.getStatus())) {
                goodStoreList.setStatus("0");
                goodStoreList.setStatusExplain(statusExplain);
            } else {
                goodStoreList.setStatus("1");
                goodStoreList.setStatusExplain(statusExplain);
            }
            boolean ok = goodStoreListService.updateById(goodStoreList);
            //TODO 返回false说明什么？
            if (ok) {
                result.success("修改成功!");
            } else {
                result.error500("修改失败！");
            }
        }
        return result;
    }

    /**
     * 通过id删除,添加删除原因
     *
     * @param id
     * @return
     */
    @AutoLog(value = "商品列表-通过id删除")
    @ApiOperation(value = "商品列表-通过id删除", notes = "商品列表-通过id删除")
    @DeleteMapping(value = "/deleteAndDelExplain")
    public Result<?> deleteAndDelExplain(@RequestParam(name = "id", required = true) String id, @RequestParam(name = "delExplain", required = true) String delExplain) {
        try {
            GoodStoreList goodStoreList = goodStoreListService.getById(id);
            goodStoreList.setDelExplain(delExplain);
            Date date = new Date();
            goodStoreList.setDelTime(date);
            //goodList.setDelFlag("1");
            goodStoreListService.updateById(goodStoreList);
            goodStoreListService.removeById(id);
        } catch (Exception e) {
            log.error("删除失败", e.getMessage());
            return Result.error("删除失败!");
        }
        return Result.ok("删除成功!");
    }

    /**
     * 返回商品列表
     *
     * @param goodStoreList
     * @param rep
     * @return
     */
    @AutoLog(value = "商品列表-放回list集合")
    @ApiOperation(value = "商品列表-放回list集合", notes = "商品列表-放回list集合")
    @RequestMapping(value = "queryList", method = RequestMethod.GET)
    public List<GoodStoreList> queryList(GoodStoreList goodStoreList,
                                         HttpServletRequest rep) {
        QueryWrapper<GoodStoreList> queryWrapper = QueryGenerator.initQueryWrapper(goodStoreList, rep.getParameterMap());
        queryWrapper.eq("is_wholesale","0");
        queryWrapper.eq("is_selected_products","0");
        List<GoodStoreList> list = goodStoreListService.list(queryWrapper);
        return list;
    }


    /**
     * 复制商品地址
     *
     * @param goodId
     * @return
     */
    @AutoLog(value = "商品列表-分页列表查询")
    @ApiOperation(value = "商品列表-分页列表查询", notes = "商品列表-分页列表查询")
    @GetMapping(value = "/getGoodUrl")
    public Result<Map<String, Object>> getGoodUrl(String goodId) {
        Result<Map<String, Object>> result = new Result<>();
        Map<String, String> map = Maps.newHashMap();
        map.put("goodId", goodId);
        map.put("isPlatform", "0");
        String url = "goodAction/pages/product/product?info=";
        Map<String, Object> mapObject = Maps.newHashMap();
        mapObject.put("url", url);
        mapObject.put("parameter", JSONObject.toJSONString(map));
        result.setResult(mapObject);
        result.setSuccess(true);
        return result;
    }

    /**
     * 更新曝光商品排序值
     *
     * @param id 商品ID
     * @param exposureSortValue 排序值
     * @return
     */
    @AutoLog(value = "曝光商品排序-更新排序值")
    @ApiOperation(value = "曝光商品排序-更新排序值", notes = "曝光商品排序-更新排序值")
    @PutMapping(value = "/updateExposureSortValue")
    public Result<?> updateExposureSortValue(@RequestBody JSONObject jsonObject) {
        String id = jsonObject.getString("id");
        Integer exposureSortValue = jsonObject.getInteger("exposureSortValue");

        // 参数验证
        if (StringUtils.isBlank(id)) {
            return Result.error("商品ID不能为空");
        }
        if (exposureSortValue == null) {
            return Result.error("排序值不能为空");
        }

        try {
            GoodStoreList goodStoreList = goodStoreListService.getById(id);
            if (goodStoreList == null) {
                return Result.error("商品不存在");
            }

            // 检查是否为曝光商品
            if (!"1".equals(goodStoreList.getIsExposureGood())) {
                return Result.error("只能设置曝光商品的排序值");
            }

            goodStoreList.setExposureSortValue(exposureSortValue);
            goodStoreListService.updateById(goodStoreList);

            return Result.ok("排序值更新成功");
        } catch (Exception e) {
            log.error("更新曝光商品排序值失败", e);
            return Result.error("更新失败：" + e.getMessage());
        }
    }

    /**
     * 批量更新曝光商品排序值
     *
     * @param sortDataList 排序数据列表
     * @return
     */
    @AutoLog(value = "曝光商品排序-批量更新排序值")
    @ApiOperation(value = "曝光商品排序-批量更新排序值", notes = "曝光商品排序-批量更新排序值")
    @PutMapping(value = "/batchUpdateExposureSortValue")
    public Result<?> batchUpdateExposureSortValue(@RequestBody List<Map<String, Object>> sortDataList) {
        try {
            for (Map<String, Object> sortData : sortDataList) {
                String id = (String) sortData.get("id");
                Integer exposureSortValue = (Integer) sortData.get("exposureSortValue");

                GoodStoreList goodStoreList = goodStoreListService.getById(id);
                if (goodStoreList != null && "1".equals(goodStoreList.getIsExposureGood())) {
                    goodStoreList.setExposureSortValue(exposureSortValue);
                    goodStoreListService.updateById(goodStoreList);
                }
            }

            return Result.ok("批量更新排序值成功");
        } catch (Exception e) {
            log.error("批量更新曝光商品排序值失败", e);
            return Result.error("批量更新失败：" + e.getMessage());
        }
    }
}
