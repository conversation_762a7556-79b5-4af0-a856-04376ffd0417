package org.jeecg.modules.member.service.impl;

import cn.hutool.core.comparator.CompareUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.util.DateUtils;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.config.jwt.utils.WeixinQRUtils;
import org.jeecg.modules.agency.vo.AgencyWorkbenchVO;
import org.jeecg.modules.alliance.vo.AllianceWorkbenchVO;
import org.jeecg.modules.edu.entity.EduClassMember;
import org.jeecg.modules.edu.service.IEduClassMemberService;
import org.jeecg.modules.marketing.entity.MarketingDistributionLevel;
import org.jeecg.modules.marketing.entity.MarketingDistributionSetting;
import org.jeecg.modules.marketing.service.*;
import org.jeecg.modules.marketing.vo.MarketingDiscountCouponVO;
import org.jeecg.modules.member.dto.MemberListDTO;
import org.jeecg.modules.member.entity.MemberAccountCapital;
import org.jeecg.modules.member.entity.MemberBankCard;
import org.jeecg.modules.member.entity.MemberDistributionLevel;
import org.jeecg.modules.member.entity.MemberList;
import org.jeecg.modules.member.mapper.MemberListMapper;
import org.jeecg.modules.member.service.*;
import org.jeecg.modules.member.utils.MemberUtils;
import org.jeecg.modules.member.utils.PromotionCodeUtils;
import org.jeecg.modules.member.utils.QrCodeUtils;
import org.jeecg.modules.member.vo.MemberCertificateVO;
import org.jeecg.modules.member.vo.MemberDiscountVO;
import org.jeecg.modules.member.vo.MemberListVO;
import org.jeecg.modules.order.service.IOrderStoreListService;
import org.jeecg.modules.pay.entity.PayBalanceLog;
import org.jeecg.modules.pay.riskcontrol.interceptor.RechargeRiskControlInterceptor;
import org.jeecg.modules.pay.service.IPayBalanceLogService;
import org.jeecg.modules.pay.utils.NotifyUrlUtils;
import org.jeecg.modules.store.entity.StoreManage;
import org.jeecg.modules.store.service.IStoreFranchiserService;
import org.jeecg.modules.store.service.IStoreManageService;
import org.jeecg.modules.store.vo.StoreManageVO;
import org.jeecg.modules.system.entity.SysSmallcode;
import org.jeecg.modules.system.service.ISysDictService;
import org.jeecg.modules.system.service.ISysSmallcodeService;
import org.jeecg.modules.system.vo.SysWorkbenchVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import java.math.BigDecimal;
import java.util.*;

/**
 * @Description: 会员列表
 * @Author: jeecg-boot
 * @Date: 2019-10-24
 * @Version: V1.0
 */
@Service
@Slf4j
public class MemberListServiceImpl extends ServiceImpl<MemberListMapper, MemberList> implements IMemberListService {

    @Autowired
    @Lazy
    private IStoreManageService iStoreManageService;

    @Autowired
    @Lazy
    private IMemberDesignationService iMemberDesignationService;
    @Autowired
    @Lazy
    private IMemberDesignationCountService iMemberDesignationCountService;

    @Autowired
    private ISysDictService iSysDictService;

    @Autowired
    private ISysSmallcodeService iSysSmallcodeService;

    @Autowired
    private WeixinQRUtils weixinQRUtils;

    @Autowired
    @Lazy
    private IMarketingGiftBagRecordService iMarketingGiftBagRecordService;

    @Autowired
    @Lazy
    private IMarketingDistributionSettingService iMarketingDistributionSettingService;

    @Autowired
    @Lazy
    private IMemberDesignationMemberListService iMemberDesignationMemberListService;

    @Autowired
    private IPayBalanceLogService iPayBalanceLogService;

    @Autowired
    private IMemberAccountCapitalService iMemberAccountCapitalService;


    @Autowired
    @Lazy
    private IMemberGiveWelfarePaymentsService iMemberGiveWelfarePaymentsService;

    @Autowired
    private NotifyUrlUtils notifyUrlUtils;

    @Autowired
    private QrCodeUtils qrCodeUtils;

    @Autowired
    private IMarketingDiscountCouponService iMarketingDiscountCouponService;

    @Autowired
    private IMarketingCertificateRecordService iMarketingCertificateRecordService;

    @Autowired
    private IStoreFranchiserService iStoreFranchiserService;

    @Autowired
    private IMemberBankCardService iMemberBankCardService;

    @Autowired
    private PromotionCodeUtils promotionCodeUtils;

    @Autowired
    private MemberUtils memberUtils;

    @Autowired
    private IMarketingDistributionLevelService iMarketingDistributionLevelService;

    @Autowired
    private IMemberDistributionLevelService iMemberDistributionLevelService;

    @Autowired
    private MemberListMapper memberListMapper;

    @Autowired
    private IOrderStoreListService iOrderStoreListService;

    @Autowired
    private IEduClassMemberService classMemberService;

    @Autowired
    @Lazy
    private org.jeecg.modules.edu.service.IEduClassMemberService eduClassMemberService;

    @Autowired
    private RechargeRiskControlInterceptor rechargeRiskControlInterceptor;

    @Override
    public Map<String, Object> getMemberInfo(String sysUserId, String memberId, String softModel) {
        Map<String, Object> memberObjectMap = Maps.newHashMap();

        //查询个人中心信息
        MemberList memberList = getById(memberId);
        //设置默认头像
        if (StringUtils.isBlank(memberList.getHeadPortrait())) {
            String memberDefaultPhotoUrl = notifyUrlUtils.getImgUrl("member_default_photo_url");
            memberList.setHeadPortrait(memberDefaultPhotoUrl);
            saveOrUpdate(memberList);
        }

        memberObjectMap.put("id", memberId);
        memberObjectMap.put("memberType", memberList.getMemberType());
        memberObjectMap.put("welfarePayments", memberList.getWelfarePayments());
        memberObjectMap.put("haveWithdrawal", memberList.getHaveWithdrawal());
        memberObjectMap.put("shareTimes", memberList.getShareTimes());
        memberObjectMap.put("isOpenStore", memberList.getIsOpenStore());
        memberObjectMap.put("phone", memberList.getPhone());
        memberObjectMap.put("accountFrozen", memberList.getAccountFrozen());
        memberObjectMap.put("unusableFrozen", memberList.getUnusableFrozen());
        memberObjectMap.put("welfarePaymentsFrozen", memberList.getWelfarePaymentsFrozen());
        memberObjectMap.put("welfarePaymentsUnusable", memberList.getWelfarePaymentsUnusable());
        memberObjectMap.put("nickName", memberList.getNickName());
        memberObjectMap.put("avatarUrl", memberList.getHeadPortrait());
        memberObjectMap.put("headPortrait", memberList.getHeadPortrait());
        memberObjectMap.put("openid", memberList.getOpenid());
        memberObjectMap.put("createTime", DateUtils.formatDate(memberList.getCreateTime(), "yyyy-MM-dd HH:mm:ss"));
        memberObjectMap.put("areaAddr", memberList.getAreaAddr());
        memberObjectMap.put("sex", memberList.getSex());
        //新增助梦家
        memberObjectMap.put("isLoveAmbassador", memberList.getIsLoveAmbassador());
        //新增员工身份
        memberObjectMap.put("isEmployee", memberList.getIsEmployee());
        //新增打榜者身份
        memberObjectMap.put("isRanker", memberList.getIsRanker());
        //是否设置交易密码
        if (StringUtils.isBlank(memberList.getTransactionPassword())) {
            memberObjectMap.put("transactionPassword", "0");
        } else {
            memberObjectMap.put("transactionPassword", "1");
        }

        if (StringUtils.isBlank(memberList.getSysSmallcodeId())) {
            //添加分享二维码信息
            addShareQr(memberList, sysUserId);
        }

        //二维码地址
        if (StringUtils.isNotBlank(memberList.getSysSmallcodeId())) {
            SysSmallcode sysSmallcode = iSysSmallcodeService.getById(memberList.getSysSmallcodeId());
            memberObjectMap.put("address", sysSmallcode != null ? sysSmallcode.getAddress() : "");
        } else {
            memberObjectMap.put("address", "");
        }

        //用户个人二维码
        if (StringUtils.isBlank(memberList.getQrcodeAddr())) {
            memberList.setQrcodeAddr(qrCodeUtils.getMemberQrCode(memberList.getId()));
            saveOrUpdate(memberList);
        }
        //生成推广码
        if (StringUtils.isBlank(memberList.getPromotionCode())) {
            memberList.setPromotionCode(promotionCodeUtils.getCode());
            saveOrUpdate(memberList);
        }
        memberObjectMap.put("promotionCode", memberList.getPromotionCode());
        memberObjectMap.put("qrcodeAddr", memberList.getQrcodeAddr());

        memberObjectMap.put("sysUserId", memberList.getSysUserId());

        //判断渠道id归属渠道id
        if (StringUtils.isNotBlank(sysUserId)) {
            String systemSharingModel = iSysDictService.queryTableDictTextByKey("sys_dict_item", "item_value", "item_text", "system_sharing_model");
            if (systemSharingModel.equals("1")) {
                memberObjectMap.put("sysUserId", sysUserId);
            }
        }

        memberObjectMap.put("totalCommission", memberList.getTotalCommission());
        memberObjectMap.put("withdrawnAmount",memberList.getHaveWithdrawal());
        memberObjectMap.put("totalRechargeBalance",memberList.getTotalRechargeBalance());

        // 使用MarketingDistributionSettingService计算会员业绩
        Map<String, BigDecimal> performanceMap = iMarketingDistributionSettingService.calculateMemberPerformance(memberList.getId());

        // 设置各类业绩数据
        memberObjectMap.put("helpFarmersPerformance", performanceMap.get("helpFarmersPerformance"));
        memberObjectMap.put("helpDisabledPerformance", performanceMap.get("helpDisabledPerformance"));
        memberObjectMap.put("helpStudentPerformance", performanceMap.get("helpStudentPerformance"));
        memberObjectMap.put("totalPerformance", performanceMap.get("totalPerformance"));

        // 获取未过期店铺补助金总额
        BigDecimal storeSubsidyAmount = getMemberValidStoreSubsidyAmount(memberId);
        
        // 获取可用店铺补助金金额（状态为可用且未过期的剩余金额）
        BigDecimal availableStoreSubsidyAmount = getMemberAvailableStoreSubsidyAmount(memberId);
        
        // 获取已过期店铺补助金金额
        BigDecimal expiredStoreSubsidyAmount = getMemberExpiredStoreSubsidyAmount(memberId);
        
        // 获取原始账户余额
        BigDecimal originalBalance = memberList.getBalance();
        
        // 添加店铺补助金金额到响应中
        memberObjectMap.put("storeSubsidyAmount", storeSubsidyAmount);
        memberObjectMap.put("availableStoreSubsidyAmount", availableStoreSubsidyAmount);
        memberObjectMap.put("expiredStoreSubsidyAmount", expiredStoreSubsidyAmount);
        
        // 设置账户余额，不再汇总店铺补助金
        memberObjectMap.put("balance", originalBalance);

        //推荐人
        memberObjectMap.put("promoterMan", memberUtils.getPromoterMan(memberList.getPromoterType(), memberList.getPromoter()));
        /**
         * 会员银行卡
         */
        MemberBankCard memberBankCard = iMemberBankCardService.getOne(new LambdaQueryWrapper<MemberBankCard>().
                eq(MemberBankCard::getMemberListId, memberId)
                .eq(MemberBankCard::getCarType, "0")
                .orderByDesc(MemberBankCard::getCreateTime)
                .last("limit 1"), false);
        if (oConvertUtils.isEmpty(memberBankCard)) {
            memberObjectMap.put("memberBankCard", "");
        } else {
            memberObjectMap.put("memberBankCard", memberBankCard.getBankName() + new StringBuilder(new StringBuilder(memberBankCard.getBankCard())
                    .reverse().toString().substring(0, 4)).reverse().toString());
            memberObjectMap.put("memberBankCardInfo", memberBankCard);
        }

        //分销称号刷新
        MemberDistributionLevel memberDistributionLevelpro = iMemberDistributionLevelService.getOne(new LambdaQueryWrapper<MemberDistributionLevel>().eq(MemberDistributionLevel::getMemberListId, memberId).orderByDesc(MemberDistributionLevel::getTeamNumber).last("limit 1"));
        if (memberDistributionLevelpro == null) {
            memberDistributionLevelpro = new MemberDistributionLevel();
            memberDistributionLevelpro.setMemberListId(memberId);
            MarketingDistributionLevel marketingDistributionLevel = iMarketingDistributionLevelService.getOne(new LambdaQueryWrapper<MarketingDistributionLevel>()
                    .eq(MarketingDistributionLevel::getStatus, "1")
                    .eq(MarketingDistributionLevel::getWaysObtain, "0")
                    .orderByAsc(MarketingDistributionLevel::getGrade)
                    .last("limit 1"));
            if (marketingDistributionLevel != null) {
                memberDistributionLevelpro.setMarketingDistributionLevelId(marketingDistributionLevel.getId());
            }
            iMemberDistributionLevelService.save(memberDistributionLevelpro);
        }
        if (StringUtils.isBlank(memberDistributionLevelpro.getMarketingDistributionLevelId())) {
            memberObjectMap.put("marketingDistributionLevelIcon", "");
        } else {
            MarketingDistributionLevel marketingDistributionLevel = iMarketingDistributionLevelService.getById(memberDistributionLevelpro.getMarketingDistributionLevelId());
            if (marketingDistributionLevel != null) {
                memberObjectMap.put("marketingDistributionLevelIcon", marketingDistributionLevel.getIcon());
            } else {
                memberObjectMap.put("marketingDistributionLevelIcon", "");
            }
        }

        //余额充值的显示和隐藏
        String balanceRechargeState = StringUtils.defaultString(iSysDictService.queryTableDictTextByKey("sys_dict_item", "item_value", "item_text", "balance_recharge_state"), "1");
        memberObjectMap.put("balanceRechargeState", balanceRechargeState);

        //开店显示：
        String openShopDisplay = iSysDictService.queryTableDictTextByKey("sys_dict_item", "item_value", "item_text", "open_shop_display");
        memberObjectMap.put("openShopDisplay", openShopDisplay);

        //判断用户是否设置密码
        if (StringUtils.isBlank(memberList.getPassword())) {
            memberObjectMap.put("isSetPassword", "0");
        } else {
            memberObjectMap.put("isSetPassword", "1");
        }
        return memberObjectMap;
    }

    @Override
    public List<MemberList> selectMemberListById(String memberListId) {
        List<MemberList> memberLists = baseMapper.selectMemberListById(memberListId);
        return memberLists;
    }


    @Override
    public IPage<MemberListVO> findMemberList(Page<MemberListVO> page, MemberListVO memberListVO) {
        return baseMapper.findMemberList(page, memberListVO);
    }

    @Override
    public IPage<MarketingDiscountCouponVO> findMemberDiscount(Page<MarketingDiscountCouponVO> page, MemberDiscountVO memberDiscountVO) {
        return baseMapper.findMemberDiscount(page, memberDiscountVO);
    }

    @Override
    public IPage<MemberCertificateVO> findMemberCertificate(Page<MemberCertificateVO> page, MemberCertificateVO memberCertificateVO) {
        return baseMapper.findMemberCertificate(page, memberCertificateVO);
    }


    @Override
    public MemberListVO findMemberDistributionCount(String memberId) {
        return baseMapper.findMemberDistributionCount(memberId);
    }

    @Override
    public IPage<Map<String, Object>> findMemberLevelList(Page<Map<String, Object>> page, String memberId) {
        return baseMapper.findMemberLevelList(page, memberId);
    }

    @Override
    public MemberListDTO getStoreSexSum(SysWorkbenchVO sysWorkbenchVO) {
        ArrayList<Map<String, Object>> memberList = new ArrayList<>();
        ArrayList<Map<String, Object>> memberSexList = new ArrayList<>();
        Map<String, Long> storeSexSum = baseMapper.getStoreSexSum(sysWorkbenchVO);
        storeSexSum.forEach((k, v) -> {
            HashMap<String, Object> mapx = new HashMap<>();
            if (k.equals("asordinarySum")) {
                mapx.put("item", "普通会员");
                mapx.put("count", String.valueOf(v));
                memberList.add(mapx);
            } else if (k.equals("vipSum")) {
                mapx.put("item", "vip");
                mapx.put("count", String.valueOf(v));
                memberList.add(mapx);
            } else if (k.equals("memberMan")) {
                mapx.put("item", "男");
                mapx.put("count", String.valueOf(v));
                memberSexList.add(mapx);
            } else if (k.equals("memberWoMan")) {
                mapx.put("item", "女");
                mapx.put("count", String.valueOf(v));
                memberSexList.add(mapx);
            } else if (k.equals("memberUnknown")) {
                mapx.put("item", "未知");
                mapx.put("count", String.valueOf(v));
                memberSexList.add(mapx);
            }
        });
        MemberListDTO memberListDTO = new MemberListDTO();
        memberListDTO.setMemberList(memberList);
        memberListDTO.setMemberSexList(memberSexList);
        memberListDTO.setMemberPatch(storeSexSum.get("asordinarySum") + storeSexSum.get("vipSum"));
        return memberListDTO;

    }

    @Override
    public AgencyWorkbenchVO getAgencySexSum(AgencyWorkbenchVO agencyWorkbenchVO) {
        ArrayList<Map<String, Object>> memberList = new ArrayList<>();
        ArrayList<Map<String, Object>> memberSexList = new ArrayList<>();
        Map<String, Object> agencySexSum = baseMapper.getAgencySexSum(agencyWorkbenchVO);
        agencySexSum.forEach((k, v) -> {
            HashMap<String, Object> mapx = new HashMap<>();
            if (k.equals("asordinarySum")) {
                mapx.put("item", "普通会员");
                mapx.put("count", new String((byte[]) v));
                memberList.add(mapx);
            } else if (k.equals("vipSum")) {
                mapx.put("item", "vip");
                mapx.put("count", new String((byte[]) v));
                memberList.add(mapx);
            } else if (k.equals("memberMan")) {
                mapx.put("item", "男");
                mapx.put("count", new String((byte[]) v));
                memberSexList.add(mapx);
            } else if (k.equals("memberWoMan")) {
                mapx.put("item", "女");
                mapx.put("count", new String((byte[]) v));
                memberSexList.add(mapx);
            } else if (k.equals("memberUnknown")) {
                mapx.put("item", "未知");
                mapx.put("count", new String((byte[]) v));
                memberSexList.add(mapx);
            }
        });
        AgencyWorkbenchVO agencyWorkbenchVO1 = new AgencyWorkbenchVO();
        agencyWorkbenchVO1.setMemberList(memberList);
        agencyWorkbenchVO1.setMemberSexList(memberSexList);
        agencyWorkbenchVO1.setMemberPatch(Long.valueOf(new String((byte[]) agencySexSum.get("asordinarySum"))) + Long.valueOf(new String((byte[]) agencySexSum.get("vipSum"))));
        return agencyWorkbenchVO1;
    }

    @Override
    public Map<String, Long> getSysSexSum(SysWorkbenchVO sysWorkbenchVO) {
        return baseMapper.getSysSexSum(sysWorkbenchVO);
    }

    @Override
    public IPage<MemberListVO> findAgencyMemberList(Page<MemberListVO> page, MemberListVO memberListVO) {
        return baseMapper.findAgencyMemberList(page, memberListVO);
    }

    @Override
    public Map<String, Object> returnPromoter(String id) {
        HashMap<String, Object> map = new HashMap<>();
        MemberList member = this.getById(id);
        if (member.getPromoterType().equals("0")) {
            if (StringUtils.isNotBlank(member.getPromoter())) {
                StoreManage storeManage = iStoreManageService.getOne(new LambdaQueryWrapper<StoreManage>()
                        .eq(StoreManage::getSysUserId, member.getPromoter())
                        .eq(StoreManage::getPayStatus, "1"));
                if (oConvertUtils.isNotEmpty(storeManage)) {
                    if (StringUtils.isNotBlank(storeManage.getSubStoreName())) {
                        map.put("promoterName", storeManage.getStoreName() + "(" + storeManage.getSubStoreName() + ")");
                    } else {
                        map.put("promoterName", storeManage.getStoreName());
                    }
                } else {
                    map.put("promoterName", "无");
                }
            }
        }
        if (member.getPromoterType().equals("1")) {
            MemberList memberList = this.getById(member.getPromoter());
            if (oConvertUtils.isNotEmpty(memberList)) {
                map.put("promoterName", memberList.getNickName() + "(" + memberList.getPhone() + ")");
            } else {
                map.put("promoterName", "无");
            }

        }
        if (member.getPromoterType().equals("2") || StringUtils.isBlank(member.getPromoterType())) {
            map.put("promoterName", "平台");
        }
        return map;
    }

    @Override
    public Map<String, Object> returnMemberNameById(String promoter, String promoterType) {
        HashMap<String, Object> map = new HashMap<>();
        if (promoterType.equals("1")) {
            MemberList member = this.getById(promoter);
            map.put("information", member.getNickName() + "(" + member.getPhone() + ")");
        }
        if (promoterType.equals("0")) {
            if (StringUtils.isNotBlank(promoter)) {
                StoreManage storeManage = iStoreManageService.getById(promoter);
                if (StringUtils.isNotBlank(storeManage.getSubStoreName())) {
                    map.put("promoter", storeManage.getStoreName() + "(" + storeManage.getSubStoreName() + ")");
                } else {
                    map.put("promoter", storeManage.getStoreName());
                }
            }
        }
        return map;
    }

    /**
     * 获取用户管理会员数据
     *
     * @param page
     * @param sysUserId
     * @param searchNickNamePhone
     * @return
     */
    @Override
    public IPage<Map<String, Object>> getMyMemberList(Page<MemberList> page, String sysUserId, String searchNickNamePhone) {
        return baseMapper.getMyMemberList(page, sysUserId, searchNickNamePhone);
    }

    @Override
    public AllianceWorkbenchVO getFranchiseeSexSum(AllianceWorkbenchVO workbench) {
        ArrayList<Map<String, Object>> memberList = new ArrayList<>();
        ArrayList<Map<String, Object>> memberSexList = new ArrayList<>();
        Map<String, Object> agencySexSum = baseMapper.getFranchiseeSexSum(workbench);
        agencySexSum.forEach((k, v) -> {
            HashMap<String, Object> mapx = new HashMap<>();
            if (k.equals("asordinarySum")) {
                mapx.put("item", "普通会员");
                mapx.put("count", String.valueOf(v));
                memberList.add(mapx);
            } else if (k.equals("vipSum")) {
                mapx.put("item", "vip");
                mapx.put("count", String.valueOf(v));
                memberList.add(mapx);
            } else if (k.equals("memberMan")) {
                mapx.put("item", "男");
                mapx.put("count", String.valueOf(v));
                memberSexList.add(mapx);
            } else if (k.equals("memberWoMan")) {
                mapx.put("item", "女");
                mapx.put("count", String.valueOf(v));
                memberSexList.add(mapx);
            } else if (k.equals("memberUnknown")) {
                mapx.put("item", "未知");
                mapx.put("count", String.valueOf(v));
                memberSexList.add(mapx);
            }
        });
        AllianceWorkbenchVO allianceWorkbenchVO = new AllianceWorkbenchVO();
        allianceWorkbenchVO.setMemberList(memberList);
        allianceWorkbenchVO.setMemberSexList(memberSexList);
        allianceWorkbenchVO.setMemberPatch((Long) agencySexSum.get("asordinarySum") + (Long) agencySexSum.get("vipSum"));
        return allianceWorkbenchVO;
    }

    @Override
    public IPage<MemberListVO> findAllianceMemberlist(Page<StoreManageVO> page, MemberListDTO memberListDTO) {
        return baseMapper.findAllianceMemberlist(page, memberListDTO);
    }

    @Override
    public List<Map<String, Object>> likeMemberByPhone(String phone) {
        return baseMapper.likeMemberByPhone(phone);
    }

    /**
     * 查询会员信息(包含删除状态)
     *
     * @param memberListId
     * @return
     */
    @Override
    public MemberList getMemberListById(String memberListId) {
        return baseMapper.getMemberListById(memberListId);
    }

    @Override
    public MemberListVO findMemberDistributionCountByMemberType(String id) {
        return baseMapper.findMemberDistributionCountByMemberType(id);
    }

    @Override
    public Long findMemberVipByMarketingGiftBag(String id) {
        return baseMapper.findMemberVipByMarketingGiftBag(id);
    }

    @Override
    public MemberListVO findMemberVipByMarketingGiftBagCount(String id) {
        return baseMapper.findMemberVipByMarketingGiftBagCount(id);
    }

    @Override
    public Long findMemberVipByMarketingGiftBagAndStraightPushId(String straightPushId, String id) {
        return baseMapper.findMemberVipByMarketingGiftBagAndStraightPushId(straightPushId, id);
    }

    @Override
    public IPage<Map<String, Object>> getmemberListByTManageId(Page<Map<String, Object>> page, String id, String memberDesignationGroupId) {
        return baseMapper.getmemberListByTManageId(page, id, memberDesignationGroupId);
    }

    @Override
    public IPage<MemberListVO> memberDesignationPageList(Page<MemberListVO> page, MemberListDTO memberListDTO) {
        return baseMapper.memberDesignationPageList(page, memberListDTO);
    }

    @Override
    public List<MemberListVO> getUnderlingList(String id) {
        return baseMapper.getUnderlingList(id);
    }

    @Override
    public void setPromoter(MemberList memberList, String tMemberId) {
        // 默认不设置绑定场景
        setPromoter(memberList, tMemberId, null);
    }

    /**
     * 设置推广人关系并记录绑定场景
     * @param memberList 会员信息
     * @param tMemberId 推广人ID
     * @param bindScene 绑定场景：0：被推荐人注册；1：被推荐人兑换产品；2：被推荐人充值助力值
     */
    @Override
    public void setPromoter(MemberList memberList, String tMemberId, String bindScene) {
        MemberList tMemberList = this.getById(tMemberId);
        if (tMemberList == null) {
            log.error("分销关系绑定失败，推广人不存在，推广人id：" + tMemberId);
            return;
        }

        //查出分销设置
        MarketingDistributionSetting marketingDistributionSetting = iMarketingDistributionSettingService.list(new LambdaQueryWrapper<MarketingDistributionSetting>()
                .eq(MarketingDistributionSetting::getDelFlag, "0")
                .eq(MarketingDistributionSetting::getStatus, "1")).get(0);

        //无门槛
        if (marketingDistributionSetting.getIsThreshold().equals("0")) {
            //推广人类型为会员
            memberList.setPromoterType("1");
            memberList.setPromoter(tMemberId);
            memberList.setMemberLevel(tMemberList.getMemberLevel() + 1);
            memberList.setMemberPath(tMemberList.getMemberPath() + "->" + memberList.getUniqueId());
            memberList.setBindTime(new Date()); // 新增绑定时间
            memberList.setBindScene(bindScene); // 设置绑定场景
            this.updatePromoter(memberList);
        }

        //需成为助梦家
        if (marketingDistributionSetting.getIsThreshold().equals("1")) {
            if (StrUtil.equals(tMemberList.getIsLoveAmbassador(), "1")) {
                //推广人类型为会员
                memberList.setPromoterType("1");
                memberList.setPromoter(tMemberId);
                memberList.setMemberLevel(tMemberList.getMemberLevel() + 1);
                memberList.setMemberPath(tMemberList.getMemberPath() + "->" + memberList.getUniqueId());
                memberList.setBindTime(new Date()); // 新增绑定时间
                memberList.setBindScene(bindScene); // 设置绑定场景
//                if (!this.updateById(memberList)) {
//                    //手动强制回滚事务，这里一定要第一时间处理
//                    log.info("需成为助梦家，绑定分销关系失败：会员id:" + memberList.getId() + "     推广人会员id:" + tMemberId + "     绑定场景:" + bindScene);
//                }
                this.updatePromoter(memberList);
            }
        }
        log.info("绑定分销关系：会员id:" + memberList.getId() + "     推广人会员id:" + tMemberId + "     绑定场景:" + bindScene);
    }

    @Override
    public List<Map<String, Object>> getDesignateMemberListByPhone(String phone) {
        return baseMapper.getDesignateMemberListByPhone(phone);
    }

    @Override
    public List<MemberList> getMemberDesignationListById(String marketingGiftBagId, String memberDesignationId) {
        return baseMapper.getMemberDesignationListById(marketingGiftBagId, memberDesignationId);
    }

    @Override
    public void setLoginRegister(MemberList memberList, String sysUserId, String tMemberId) {

        //添加会员分享关系
        memberList.setSysUserId(sysUserId);
        //查出分销设置
        MarketingDistributionSetting marketingDistributionSetting = iMarketingDistributionSettingService.getOne(new LambdaQueryWrapper<MarketingDistributionSetting>()
                .eq(MarketingDistributionSetting::getDelFlag, "0")
                .eq(MarketingDistributionSetting::getStatus, "1")
                .last("limit 1"));

        if (marketingDistributionSetting != null && StringUtils.isNotBlank(tMemberId)) {


            /**
             * 字典名称: system_sharing_model 系统分享模式：0：分享绑定归属店铺；1：分享绑定渠道店铺
             */
            String systemSharingModel = iSysDictService.queryTableDictTextByKey("sys_dict_item", "item_value", "item_text", "system_sharing_model");

            if (systemSharingModel.equals("0")) {
                memberList.setSysUserId(this.getById(tMemberId).getSysUserId());
            }

            if (systemSharingModel.equals("1")) {
                memberList.setSysUserId(sysUserId);
            }


            log.info("推广人id：" + tMemberId + "；推广店铺id：" + sysUserId);
            //推广人id不为空
            if (marketingDistributionSetting.getDistributionBuild().contains("0")) {
                this.setPromoter(memberList, tMemberId, "0"); // 传递绑定场景参数"0"表示被推荐人注册

            }
            //称号
//            LambdaQueryWrapper<MemberDesignation> memberDesignationLambdaQueryWrapper = new LambdaQueryWrapper<MemberDesignation>()
//                    .eq(MemberDesignation::getDelFlag, "0")
//                    .eq(MemberDesignation::getStatus, "1")
//                    .eq(MemberDesignation::getIsDefault, "1");
//            if (iMemberDesignationService.count(memberDesignationLambdaQueryWrapper) > 0) {
//                if (iMemberDesignationMemberListService.count(new LambdaQueryWrapper<MemberDesignationMemberList>()
//                        .eq(MemberDesignationMemberList::getDelFlag, "0")
//                        .eq(MemberDesignationMemberList::getMemberListId, memberList.getId())
//                ) <= 0) {
//                    MemberDesignation memberDesignation = iMemberDesignationService
//                            .list(memberDesignationLambdaQueryWrapper
//                                    .isNull(MemberDesignation::getMemberDesignationGroupId)).get(0);
//
//                    iMemberDesignationMemberListService.save(new MemberDesignationMemberList()
//                            .setDelFlag("0")
//                            .setMemberListId(memberList.getId())
//                            .setMemberDesignationId(memberDesignation.getId())
//                            .setMemberJoinTime(new Date())
//                    );
//
//                    if (iMemberDesignationCountService.count(new LambdaQueryWrapper<MemberDesignationCount>()
//                            .eq(MemberDesignationCount::getDelFlag, "0")
//                            .eq(MemberDesignationCount::getMemberDesignationId, memberDesignation.getId())
//                            .eq(MemberDesignationCount::getMemberListId, memberList.getId())
//                    ) <= 0) {
//                        iMemberDesignationCountService.save(new MemberDesignationCount()
//                                .setDelFlag("0")
//                                .setMemberDesignationId(memberDesignation.getId())
//                                .setMemberListId(memberList.getId())
//                                .setTotalMembers(new BigDecimal(1))
//                        );
//                    }
//                }
//                /*if (StringUtils.isBlank(memberList.getOldTManageId())){
//                    this.memberListSetTManageId(memberList,tMemberId);
//                }*/
//            }

            /*if (StringUtils.isBlank(memberList.getOldTManageId())){
                this.memberListSetTManageId(memberList,tMemberId);
            }*/
        } else {
            if (StringUtils.isNotBlank(sysUserId)) {
                //推广人类型为店铺
                memberList.setPromoterType("0");
                memberList.setPromoter(sysUserId);
            }
        }
        if (!this.updateById(memberList)) {
            //手动强制回滚事务，这里一定要第一时间处理
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
        }
        
        // 联动更新班级学员状态（会员注册场景）
        try {
            if (StringUtils.isNotBlank(memberList.getPhone())) {
                eduClassMemberService.updateClassMemberStatusByMemberPhone(
                    memberList.getPhone(), 
                    memberList.getStatus(), 
                    memberList.getIsLoveAmbassador(), 
                    "system_register"
                );
            }
        } catch (Exception e) {
            log.error("会员注册联动更新班级学员状态失败，会员ID: {}, 手机号: {}", 
                    memberList.getId(), memberList.getPhone(), e);
        }
    }


    @Override
    public void updatePromoter(MemberList memberList) {
        this.baseMapper.updatePromoter(memberList);
    }

    @Override
    public void setLoginRegister(MemberList memberList, String tMemberId) {
        //首次注册先绑定到平台运营账号下
        memberList = getById(memberList.getId());
        MemberList systemOperation = getOne(new LambdaQueryWrapper<MemberList>()
                .eq(MemberList::getIsSystemOperation, "1"),false);
        memberList.setPromoterType("2");
        if (systemOperation != null) {
            memberList.setMemberLevel(systemOperation.getMemberLevel() + 1);
            memberList.setMemberPath(systemOperation.getMemberPath() + "->" + Convert.toStr(memberList.getUniqueId()));
            memberList.setPromoter(systemOperation.getId());
        } else {
            memberList.setMemberLevel(1);
            memberList.setMemberPath(Convert.toStr(memberList.getUniqueId()));
        }
        updateById(memberList);
    }

    @Override
    public void addShareQr(MemberList memberList, String sysUserId) {
        //增加分享二维码
        if (StringUtils.isBlank(memberList.getSysSmallcodeId())) {
            SysSmallcode sysSmallcode = new SysSmallcode();
            sysSmallcode.setSysUserId(memberList.getSysUserId());

            //判断渠道id归属渠道id
            if (StringUtils.isNotBlank(sysUserId)) {
                String systemSharingModel = iSysDictService.queryTableDictTextByKey("sys_dict_item", "item_value", "item_text", "system_sharing_model");
                if (systemSharingModel.equals("1")) {
                    sysSmallcode.setSysUserId(sysUserId);
                }
            }

            sysSmallcode.setTMemberId(memberList.getId());
            sysSmallcode.setCodeType("1");

            // 【修复】设置分享参数，确保扫码进入时能正确解析tMemberId
            Map<String, Object> shareParams = new HashMap<>();
            shareParams.put("tMemberId", memberList.getId());
            shareParams.put("TmemberName", memberList.getNickName() != null ? memberList.getNickName() : "");
            shareParams.put("TmemberHeadPortrait", memberList.getHeadPortrait() != null ? memberList.getHeadPortrait() : "");
            shareParams.put("phone", memberList.getPhone() != null ? memberList.getPhone() : "");
            shareParams.put("sysUserId", sysSmallcode.getSysUserId() != null ? sysSmallcode.getSysUserId() : "");
            shareParams.put("shareType", "2"); // 二维码分享
            sysSmallcode.setParam(JSON.toJSONString(shareParams));

            iSysSmallcodeService.save(sysSmallcode);
            memberList.setSysSmallcodeId(sysSmallcode.getId());
            String shareControl = iSysDictService.queryTableDictTextByKey("sys_dict_item", "item_value", "item_text", "share_control");
            String address = "";
            if (shareControl.equals("1")) {
                address = weixinQRUtils.getQrCodeNew(sysSmallcode.getId(),"pages/index/index");
            } else {
                String shareUrl = notifyUrlUtils.getBseUrl("share_url");
                String param = "?tMemberId=" + sysSmallcode.getTMemberId() + "&tMemberName=" + memberList.getNickName() + "&tMemberHeadportrait=" + memberList.getHeadPortrait();
                address = qrCodeUtils.getMemberQrCode(shareUrl + param);
            }
            if (StringUtils.isBlank(address)) {
                return;
            }
            sysSmallcode.setAddress(address);
            iSysSmallcodeService.saveOrUpdate(sysSmallcode);
            this.saveOrUpdate(memberList);
        }
    }


    @Override
    @Transactional
    public boolean addBlance(String memberId, BigDecimal balance, String orderNo, String payType) {
        return addBlance(memberId, balance, orderNo, payType, 0);
    }

    @Override
    @Transactional
    public boolean addBlance(String memberId, BigDecimal balance, String orderNo, String payType, String remarks) {
        return addBlance(memberId, balance, orderNo, payType, 0, remarks);
    }

    @Override
    @Transactional
    public boolean addBlance(String memberId, BigDecimal balance, String orderNo, String payType, Integer memberSource) {
        return addBlance(memberId, balance, orderNo, payType, memberSource, null);
    }

    @Override
    @Transactional
    public boolean addBlance(String memberId, BigDecimal balance, String orderNo, String payType, Integer memberSource, String remarks) {
        MemberList memberList = this.getById(memberId);

        if (memberList == null) {
            return false;
        }

        if (balance.doubleValue() > 0) {

            log.info("余额增加，会员id：" + memberId + ";单号：" + orderNo + ";交易类型：" + payType);

            //会员资金加入余额
            memberList.setBalance(memberList.getBalance().add(balance));


            //生成资金流水记录
            MemberAccountCapital memberAccountCapital = new MemberAccountCapital()
                    .setMemberListId(memberId)
                    .setPayType(payType)//代表订单交易
                    .setGoAndCome("0")//支付和收入；0：收入；1：支出
                    .setAmount(balance)
                    .setOrderNo(orderNo)
                    .setTradeNo(orderNo)
                    .setBalance(memberList.getBalance())
                    .setMemberSource(ObjectUtil.defaultIfNull(memberSource, 0));

            // 设置备注
            if (StringUtils.isNotBlank(remarks)) {
                memberAccountCapital.setRemarks(remarks);
            }

            //生成资金流水记录
            iMemberAccountCapitalService.save(memberAccountCapital);
            return this.updateById(memberList);
        }
        return true;
    }

    /**
     * 店铺补助金发放专用方法
     */
    @Override
    @Transactional
    public boolean addBlance(String memberId, BigDecimal balance, String orderNo, String payType, String remarks, Date expireTime, String subsidyStoreId) {
        MemberList memberList = this.getById(memberId);

        if (memberList == null) {
            return false;
        }

        if (balance.doubleValue() > 0) {
            log.info("店铺补助金增加，会员id：" + memberId + ";单号：" + orderNo + ";交易类型：" + payType);

            //生成资金流水记录
            MemberAccountCapital memberAccountCapital = new MemberAccountCapital()
                    .setMemberListId(memberId)
                    .setPayType(payType) // 50表示店铺补助金
                    .setGoAndCome("0") // 支付和收入；0：收入；1：支出
                    .setAmount(balance)
                    .setOrderNo(orderNo)
                    .setTradeNo(orderNo)
                    .setBalance(memberList.getBalance())
                    .setMemberSource(0); // 默认为全惠付

            // 设置店铺补助金特有字段
            memberAccountCapital.setExpireTime(expireTime);
            memberAccountCapital.setSubsidyStoreId(subsidyStoreId);
            
            // 剩余可用金额默认等于发放金额
            memberAccountCapital.setRemainingAmount(balance);
            
            // 设置店铺补助金状态为可用(0)
            memberAccountCapital.setStoreSubsidyStatus("0");
            
            // 设置备注
            if (StringUtils.isNotBlank(remarks)) {
                memberAccountCapital.setRemarks(remarks);
            }

            //生成资金流水记录
            iMemberAccountCapitalService.save(memberAccountCapital);
            return this.updateById(memberList);
        }
        return true;
    }

    @Override
    @Transactional(propagation = Propagation.NESTED)
    public boolean subtractBlance(String memberId, BigDecimal balance, String orderNo, String payType) {
        return subtractBlance(memberId, balance, orderNo, payType, 0);
    }

    @Override
    @Transactional(propagation = Propagation.NESTED)
    public boolean subtractBlance(String memberId, BigDecimal balance, String orderNo, String payType, String remarks) {
        return subtractBlance(memberId, balance, orderNo, payType, 0, remarks);
    }

    @Override
    @Transactional(propagation = Propagation.NESTED)
    public boolean subtractBlance(String memberId, BigDecimal balance, String orderNo, String payType, Integer memberSource) {
        return subtractBlance(memberId, balance, orderNo, payType, memberSource, null);
    }

    @Override
    @Transactional(propagation = Propagation.NESTED)
    public boolean subtractBlance(String memberId, BigDecimal balance, String orderNo, String payType, Integer memberSource, String remarks) {
        MemberList memberList = this.getById(memberId);
        if (memberList == null) {
            return false;
        }
        if (balance.doubleValue() > 0) {

            log.info("余额减少，会员id：" + memberId + ";单号：" + orderNo + ";交易类型：" + payType);

            //会员资金加入余额
            memberList.setBalance(memberList.getBalance().subtract(balance));

            //生成资金流水记录
            MemberAccountCapital memberAccountCapital = new MemberAccountCapital()
                    .setMemberListId(memberId)
                    .setPayType(payType)//代表订单交易
                    .setGoAndCome("1")//支付和收入；0：收入；1：支出
                    .setAmount(balance)
                    .setOrderNo(orderNo)
                    .setTradeNo(orderNo)
                    .setBalance(memberList.getBalance())
                    .setMemberSource(ObjectUtil.defaultIfNull(memberSource, 0));

            // 设置备注
            if (StringUtils.isNotBlank(remarks)) {
                memberAccountCapital.setRemarks(remarks);
            }

            //生成资金流水记录
            iMemberAccountCapitalService.save(memberAccountCapital);
            return this.updateById(memberList);
        }
        return true;
    }

    @Override
    public void rechargeBalance(String payBalanceLogId) {
        log.info("[余额充值业务] 开始处理余额充值，PayBalanceLogId：{}", payBalanceLogId);

        // 1. 获取支付日志并更新状态
        PayBalanceLog payBalanceLog = iPayBalanceLogService.getById(payBalanceLogId);
        if (payBalanceLog == null) {
            log.error("[余额充值业务] PayBalanceLog查询为空，PayBalanceLogId：{}", payBalanceLogId);
            throw new RuntimeException("支付日志不存在");
        }

        log.info("[余额充值业务] PayBalanceLog信息 - 会员ID：{}, 充值金额：{}, 当前支付状态：{}, 分享人ID：{}",
                 payBalanceLog.getMemberListId(), payBalanceLog.getTotalFee(),
                 payBalanceLog.getPayStatus(), payBalanceLog.getTMemberId());

        payBalanceLog.setPayStatus("1");
        if (!iPayBalanceLogService.updateById(payBalanceLog)) {
            log.error("[余额充值业务] PayBalanceLog状态更新失败，PayBalanceLogId：{}", payBalanceLogId);
            //手动强制回滚事务，这里一定要第一时间处理
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return;
        }
        log.info("[余额充值业务] PayBalanceLog状态更新成功，支付状态已设为：1");

        // 2. 获取会员信息并更新累计充值金额
        MemberList memberList = this.getById(payBalanceLog.getMemberListId());
        if (memberList == null) {
            log.error("[余额充值业务] 会员信息查询为空，会员ID：{}", payBalanceLog.getMemberListId());
            throw new RuntimeException("会员信息不存在");
        }

        BigDecimal originalTotalRecharge = memberList.getTotalRechargeBalance();
        BigDecimal newTotalRecharge = NumberUtil.add(memberList.getTotalRechargeBalance(), payBalanceLog.getTotalFee());
        memberList.setTotalRechargeBalance(newTotalRecharge);

        log.info("[余额充值业务] 会员累计充值金额更新 - 会员ID：{}, 原累计金额：{}, 本次充值：{}, 新累计金额：{}",
                 memberList.getId(), originalTotalRecharge, payBalanceLog.getTotalFee(), newTotalRecharge);

        // 3. 查询分销设置并判断助梦家升级
        MarketingDistributionSetting marketingDistributionSetting = iMarketingDistributionSettingService.list(new LambdaQueryWrapper<MarketingDistributionSetting>()
                .eq(MarketingDistributionSetting::getDelFlag, "0")
                .eq(MarketingDistributionSetting::getStatus, "1")).get(0);

        BigDecimal loveAmbassadorThreshold = ObjectUtil.defaultIfNull(marketingDistributionSetting.getLoveAmbassador(), new BigDecimal("99"));
        boolean wasLoveAmbassador = "1".equals(memberList.getIsLoveAmbassador());

        log.info("[余额充值业务] 助梦家升级判断 - 当前是否助梦家：{}, 累计充值：{}, 升级阈值：{}",
                 wasLoveAmbassador, newTotalRecharge, loveAmbassadorThreshold);

        //累计金额达到99直接升级为助梦家
        if (CompareUtil.compare(memberList.getTotalRechargeBalance(), loveAmbassadorThreshold) >= 0) {
            if (!wasLoveAmbassador) {
                memberList.setIsLoveAmbassador("1");
                //推荐人在开班期间邀请充值并激活，系统奖励5助力值
                String tMemberId = payBalanceLog.getTMemberId();
                if (StringUtils.isNotBlank(tMemberId)) {
                    try {
                        //实时查找推荐人当前在读班级
                        LambdaQueryWrapper<EduClassMember> eduClassMemberLambdaQueryWrapper = new LambdaQueryWrapper<>();
                        eduClassMemberLambdaQueryWrapper.eq(EduClassMember::getMemberId,tMemberId)
                                .eq(EduClassMember::getStudentClassStatus,"STUDYING")
                                .orderByDesc(EduClassMember::getCreateTime)
                                .last("limit 1");
                        EduClassMember eduClassMember = eduClassMemberService.getOne(eduClassMemberLambdaQueryWrapper, false);
                        if (eduClassMember != null) {
                            //不为空表示当前推荐人有在读班级，即可发放5助力值奖励
                            BigDecimal bigDecimal = new BigDecimal(5);
                            if (this.addBlance(tMemberId,bigDecimal,payBalanceLogId,"58","推荐人在班级[" +eduClassMember.getClassId() + "]开班期间邀请充值并激活，系统奖励5助力值")) {
                                //更新累计奖励金额
                                eduClassMember.setClassPeriodRewardsTotal(NumberUtil.add(bigDecimal,eduClassMember.getClassPeriodRewardsTotal()));
                                eduClassMemberService.updateById(eduClassMember);
                            }
                        }
                    } catch (Exception e) {
                        log.error("推荐人在开班期间邀请充值并激活，系统奖励5助力值失败。支付id：{},推荐人id：{}",payBalanceLogId,tMemberId);
                    }
                }
                log.info("[余额充值业务] 会员升级为助梦家，会员ID：{}, 累计充值金额：{}",
                         memberList.getId(), newTotalRecharge);
            } else {
                log.info("[余额充值业务] 会员已是助梦家，无需升级");
            }
        } else {
            log.info("[余额充值业务] 累计充值金额未达到助梦家升级条件");
        }

        if (!this.updateById(memberList)) {
            log.error("[余额充值业务] 会员信息更新失败，会员ID：{}", memberList.getId());
            //手动强制回滚事务，这里一定要第一时间处理
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return;
        }
        log.info("[余额充值业务] 会员信息更新成功");

        // 4. 增加会员余额
        log.info("[余额充值业务] 开始增加会员余额 - 会员ID：{}, 充值金额：{}, 交易类型：18",
                 payBalanceLog.getMemberListId(), payBalanceLog.getTotalFee());

        if (!this.addBlance(payBalanceLog.getMemberListId(), payBalanceLog.getTotalFee(), payBalanceLog.getId(), "18")) {
            log.error("[余额充值业务] 会员余额增加失败，会员ID：{}, 充值金额：{}",
                      payBalanceLog.getMemberListId(), payBalanceLog.getTotalFee());
            //手动强制回滚事务，这里一定要第一时间处理
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return;
        }
        log.info("[余额充值业务] 会员余额增加成功");

        // 5. 分销关系绑定处理
        log.info("[余额充值业务] 开始检查分销关系绑定 - 会员类型：{}, 推广人类型：{}, 当前推广人：{}, 分享人ID：{}",
                 memberList.getMemberType(), memberList.getPromoterType(),
                 memberList.getPromoter(), payBalanceLog.getTMemberId());

        // 充值是否要绑定分销关系 @2021-03-25 15:00 by:zhangshaolin
        if (StrUtil.equals(memberList.getPromoterType(),"2")
                && StrUtil.equals(memberList.getMemberType(),"0") && StrUtil.isBlank(memberList.getPromoter())) {
            //自然客还未绑定实际推广人
            String tMemberId = payBalanceLog.getTMemberId();
            if (StringUtils.isNotBlank(tMemberId)) {
                log.info("[余额充值业务] 符合分销关系绑定条件，开始处理绑定逻辑");

                // 获取分销设置
                MarketingDistributionSetting distributionSetting = iMarketingDistributionSettingService.lambdaQuery()
                        .eq(MarketingDistributionSetting::getStatus, "1")
                        .orderByDesc(MarketingDistributionSetting::getCreateTime)
                        .last("limit 1")
                        .one();

                if (distributionSetting != null) {
                    log.info("[余额充值业务] 分销设置查询成功，分销构建配置：{}", distributionSetting.getDistributionBuild());

                    // 检查是否配置了"被推荐人充值助力值"场景（值为2）
                    if (StringUtils.isNotBlank(distributionSetting.getDistributionBuild()) &&
                            distributionSetting.getDistributionBuild().contains("2")) {
                        log.info("[余额充值业务] 配置了充值绑定分销关系，开始绑定推广人：{}", tMemberId);
                        this.setPromoter(memberList, tMemberId, "2"); // 传递绑定场景参数"2"表示被推荐人充值助力值
                        log.info("[余额充值业务] 分销关系绑定完成");
                    } else {
                        log.info("[余额充值业务] 未配置充值绑定分销关系，跳过绑定");
                    }
                } else {
                    log.warn("[余额充值业务] 分销设置查询为空，无法进行分销关系绑定");
                }
            } else {
                log.info("[余额充值业务] 分享人ID为空，无法绑定分销关系");
            }
        } else {
            log.info("[余额充值业务] 不符合分销关系绑定条件，跳过绑定处理");
        }

        // 6. 联动更新班级学员状态（充值成功场景）
        log.info("[余额充值业务] 开始联动更新班级学员状态 - 会员手机号：{}", memberList.getPhone());
        try {
            if (StringUtils.isNotBlank(memberList.getPhone())) {
                eduClassMemberService.updateClassMemberStatusByMemberPhone(
                    memberList.getPhone(),
                    memberList.getStatus(),
                    memberList.getIsLoveAmbassador(),
                    "system_recharge"
                );
                log.info("[余额充值业务] 班级学员状态联动更新成功");
            } else {
                log.info("[余额充值业务] 会员手机号为空，跳过班级学员状态更新");
            }
        } catch (Exception e) {
            log.error("[余额充值业务] 充值成功联动更新班级学员状态失败，会员ID: {}, 手机号: {}",
                    memberList.getId(), memberList.getPhone(), e);
        }

        // 7. 充值成功后的风控后置处理
        log.info("[余额充值业务] 开始风控后置处理");
        try {
            rechargeRiskControlInterceptor.onRechargeSuccess(payBalanceLog.getMemberListId(), payBalanceLog.getTotalFee());
            log.info("[余额充值业务] 充值成功风控后置处理完成，会员ID: {}, 金额: {}",
                    payBalanceLog.getMemberListId(), payBalanceLog.getTotalFee());
        } catch (Exception e) {
            log.error("[余额充值业务] 充值成功风控后置处理异常，会员ID: {}, 金额: {}",
                    payBalanceLog.getMemberListId(), payBalanceLog.getTotalFee(), e);
            // 后置处理异常不影响主业务流程
        }

        log.info("[余额充值业务] 余额充值业务处理完成，PayBalanceLogId：{}, 会员ID：{}, 充值金额：{}",
                 payBalanceLogId, payBalanceLog.getMemberListId(), payBalanceLog.getTotalFee());
    }

    @Override
    public IPage<Map<String, Object>> pushingNumber(Page<Map<String, Object>> page, String memberId) {
        return baseMapper.pushingNumber(page, memberId);
    }

    @Override
    public int betweenPush(String memberId) {
        return baseMapper.betweenPush(memberId);
    }

    @Override
    public Map<String, BigDecimal> getMemberBusinessPerformance(String memberId, String startDate, String endDate) {
        Map<String, BigDecimal> performanceMap = new HashMap<>();

        // 初始化业绩为0
        performanceMap.put("totalPerformance", BigDecimal.ZERO);
        performanceMap.put("directPerformance", BigDecimal.ZERO);
        performanceMap.put("indirectPerformance", BigDecimal.ZERO);
        performanceMap.put("helpFarmersPerformance", BigDecimal.ZERO);
        performanceMap.put("helpDisabledPerformance", BigDecimal.ZERO);
        performanceMap.put("helpStudentPerformance", BigDecimal.ZERO);

        // 获取会员信息
        MemberList member = getById(memberId);
        if (member == null) {
            return performanceMap;
        }

        // 获取会员唯一标识
        String uniqueId = member.getUniqueId().toString();

        // 1. 计算总业绩（包含自身 + 自身关系链下的所有业绩）
        List<Map<String, Object>> totalPerformanceList = memberListMapper.queryMemberTotalPerformance(memberId, uniqueId, startDate, endDate);
        if (totalPerformanceList != null && !totalPerformanceList.isEmpty()) {
            BigDecimal totalPerformance = BigDecimal.ZERO;

            for (Map<String, Object> item : totalPerformanceList) {
                String orderType = Convert.toStr(item.get("orderType"));
                BigDecimal performance = Convert.toBigDecimal(item.get("performance")).setScale(2, BigDecimal.ROUND_HALF_UP);
                totalPerformance = totalPerformance.add(performance);

                // 根据订单类型记录不同类型的业绩
                if ("15".equals(orderType)) {
                    performanceMap.put("helpFarmersPerformance", performance);
                } else if ("16".equals(orderType)) {
                    performanceMap.put("helpDisabledPerformance", performance);
                } else if ("17".equals(orderType)) {
                    performanceMap.put("helpStudentPerformance", performance);
                }
            }

            performanceMap.put("totalPerformance", totalPerformance);
        }

        // 2. 计算直接业绩（自身 + 直接下级的业绩）
        List<Map<String, Object>> directPerformanceList = memberListMapper.queryMemberDirectPerformance(memberId, uniqueId, startDate, endDate);
        if (directPerformanceList != null && !directPerformanceList.isEmpty()) {
            BigDecimal directPerformance = BigDecimal.ZERO;

            for (Map<String, Object> item : directPerformanceList) {
                BigDecimal performance = Convert.toBigDecimal(item.get("performance")).setScale(2, BigDecimal.ROUND_HALF_UP);
                directPerformance = directPerformance.add(performance);
            }

            performanceMap.put("directPerformance", directPerformance);
        }

        // 3. 计算间接业绩（总业绩 - 直接业绩）
        BigDecimal totalPerformance = performanceMap.get("totalPerformance");
        BigDecimal directPerformance = performanceMap.get("directPerformance");

        if (totalPerformance.compareTo(BigDecimal.ZERO) > 0 && directPerformance.compareTo(BigDecimal.ZERO) > 0) {
            BigDecimal indirectPerformance = totalPerformance.subtract(directPerformance)
                    .setScale(2, BigDecimal.ROUND_HALF_UP);
            performanceMap.put("indirectPerformance", indirectPerformance);
        }

        return performanceMap;
    }

    @Override
    public Map<String, Object> getMemberDistributionCommission(String memberId, String startDate, String endDate) {
        Map<String, Object> resultMap = new HashMap<>();
        Map<String, BigDecimal> commissionMap = new HashMap<>();
        Map<String, Integer> orderCountMap = new HashMap<>();

        // 初始化佣金数据
        commissionMap.put("totalCommission", BigDecimal.ZERO);
        commissionMap.put("brandCommission", BigDecimal.ZERO);       // 品牌馆佣金
        commissionMap.put("lifeCommission", BigDecimal.ZERO);        // 生活馆佣金
        commissionMap.put("entrepreneurCommission", BigDecimal.ZERO); // 创业馆佣金

        // 初始化订单数量数据
        orderCountMap.put("totalOrderCount", 0);
        orderCountMap.put("brandOrderCount", 0);       // 品牌馆订单数量
        orderCountMap.put("lifeOrderCount", 0);        // 生活馆订单数量
        orderCountMap.put("entrepreneurOrderCount", 0); // 创业馆订单数量

        // 查询分销佣金数据
        List<Map<String, Object>> commissionList = memberListMapper.queryMemberCommission(memberId, startDate, endDate);
        if (commissionList != null && !commissionList.isEmpty()) {
            BigDecimal totalCommission = BigDecimal.ZERO;

            for (Map<String, Object> item : commissionList) {
                String orderType = Convert.toStr(item.get("orderType"));
                BigDecimal commission = Convert.toBigDecimal(item.get("commission")).setScale(2, BigDecimal.ROUND_HALF_UP);
                totalCommission = totalCommission.add(commission);

                // 根据订单类型分类佣金
                if ("15".equals(orderType)) {
                    // 品牌馆订单
                    commissionMap.put("brandCommission", commission);
                } else if ("16".equals(orderType)) {
                    // 生活馆订单
                    commissionMap.put("lifeCommission", commission);
                } else if ("17".equals(orderType)) {
                    // 创业馆订单
                    commissionMap.put("entrepreneurCommission", commission);
                }
            }

            commissionMap.put("totalCommission", totalCommission);
        }

        // 查询分销订单数量
        List<Map<String, Object>> orderCountList = memberListMapper.queryMemberCommissionOrderCount(memberId, startDate, endDate);
        if (orderCountList != null && !orderCountList.isEmpty()) {
            int totalOrderCount = 0;

            for (Map<String, Object> item : orderCountList) {
                String orderType = Convert.toStr(item.get("orderType"));
                int orderCount = Convert.toInt(item.get("orderCount"));
                totalOrderCount += orderCount;

                // 根据订单类型分类订单数量
                if ("15".equals(orderType)) {
                    // 品牌馆订单
                    orderCountMap.put("brandOrderCount", orderCount);
                } else if ("16".equals(orderType)) {
                    // 生活馆订单
                    orderCountMap.put("lifeOrderCount", orderCount);
                } else if ("17".equals(orderType)) {
                    // 创业馆订单
                    orderCountMap.put("entrepreneurOrderCount", orderCount);
                }
            }

            orderCountMap.put("totalOrderCount", totalOrderCount);
        }

        // 将佣金和订单数量数据合并到结果中
        resultMap.putAll(commissionMap);
        resultMap.putAll(orderCountMap);

        return resultMap;
    }

    @Autowired
    private ISysDictService sysDictService;

    @Override
    public IPage<Map<String, Object>> getMemberDistributionOrders(Page<Map<String, Object>> page, String memberId, String startDate, String endDate, String orderConfirmStatus) {
        // 使用分页参数查询分销订单
        // 如果未传入订单确认状态，默认为-1（查询全部分销订单）
        if (orderConfirmStatus == null || orderConfirmStatus.isEmpty()) {
            orderConfirmStatus = "-1";
        }
        IPage<Map<String, Object>> result = memberListMapper.queryMemberDistributionOrders(page, memberId, startDate, endDate, orderConfirmStatus);
        List<Map<String, Object>> orderList = result.getRecords();

        // 处理订单数据，区分已确认和未确认佣金
        for (Map<String, Object> order : orderList) {
            String orderStatus = Convert.toStr(order.get("orderStatus"));
            BigDecimal distributionCommission = Convert.toBigDecimal(order.get("distributionCommission")).setScale(2, BigDecimal.ROUND_HALF_UP);

            // 判断订单状态，添加已确认和未确认佣金字段
            if ("3".equals(orderStatus) || "5".equals(orderStatus)) {
                // 已完成或已收货的订单，佣金已确认
                order.put("confirmedCommission", distributionCommission);
                order.put("unconfirmedCommission", BigDecimal.ZERO);
                order.put("commissionStatus", "1"); // 1: 已确认
            } else {
                // 其他状态的订单，佣金未确认
                order.put("confirmedCommission", BigDecimal.ZERO);
                order.put("unconfirmedCommission", distributionCommission);
                order.put("commissionStatus", "0"); // 0: 未确认
            }

            // 使用字典服务进行订单类型文本转换
            String orderType = Convert.toStr(order.get("orderType"));
            // 使用订单类型字典（假设字典编码为 order_type）
            String orderTypeText = sysDictService.queryDictTextByKey("order_type", orderType);
            if (StringUtils.isNotBlank(orderTypeText)) {
                order.put("orderType_dictText", orderTypeText);
            } else {
                // 如果字典中没有对应的值，使用默认映射
                if ("15".equals(orderType)) {
                    order.put("orderType_dictText", "品牌馆");
                } else if ("16".equals(orderType)) {
                    order.put("orderType_dictText", "生活馆");
                } else if ("17".equals(orderType)) {
                    order.put("orderType_dictText", "创业馆");
                } else {
                    order.put("orderType_dictText", "其他");
                }
            }

            // 使用字典服务进行订单状态文本转换
            // 使用订单状态字典（假设字典编码为 order_status）
            String orderStatusText = sysDictService.queryDictTextByKey("order_status", orderStatus);
            if (StringUtils.isNotBlank(orderStatusText)) {
                order.put("orderStatus_dictText", orderStatusText);
            } else {
                // 如果字典中没有对应的值，使用默认映射
                if ("0".equals(orderStatus)) {
                    order.put("orderStatus_dictText", "待付款");
                } else if ("1".equals(orderStatus)) {
                    order.put("orderStatus_dictText", "待发货");
                } else if ("2".equals(orderStatus)) {
                    order.put("orderStatus_dictText", "待收货");
                } else if ("3".equals(orderStatus)) {
                    order.put("orderStatus_dictText", "已完成");
                } else if ("4".equals(orderStatus)) {
                    order.put("orderStatus_dictText", "已关闭");
                } else if ("5".equals(orderStatus)) {
                    order.put("orderStatus_dictText", "已收货");
                } else {
                    order.put("orderStatus_dictText", "未知状态");
                }
            }
        }

        return result;
    }

    @Override
    public IPage<Map<String, Object>> getTeamDetailList(Page<Map<String, Object>> page, String memberId, String startDate, String endDate) {
        // 使用分页参数查询团队明细
        IPage<Map<String, Object>> result = memberListMapper.queryTeamDetailList(page, memberId, startDate, endDate);
        List<Map<String, Object>> teamList = result.getRecords();

        // 处理团队成员数据，格式化金额字段并计算影响力
        for (Map<String, Object> member : teamList) {
            // 格式化直接业绩
            BigDecimal directPerformance = Convert.toBigDecimal(member.get("directPerformance")).setScale(2, BigDecimal.ROUND_HALF_UP);
            member.put("directPerformance", directPerformance);

            // 格式化总佣金
            BigDecimal totalCommission = Convert.toBigDecimal(member.get("totalCommission")).setScale(2, BigDecimal.ROUND_HALF_UP);
            member.put("totalCommission", totalCommission);

            // 计算影响力分数（全部历史数据）
            String memberListId = Convert.toStr(member.get("memberId"));
            String memberPath = Convert.toStr(member.get("memberPath"));
            BigDecimal influenceScore = calculateInfluenceScoreForTeamMember(memberListId, memberPath);
            member.put("influenceScore", influenceScore);
        }

        return result;
    }

    /**
     * 计算团队成员的影响力分数（全部历史数据）
     * 影响力分数 = 个人以及个人递归层级下所有人的充值总金额
     *
     * @param memberListId 会员ID
     * @param memberPath 会员路径
     * @return 影响力分数
     */
    private BigDecimal calculateInfluenceScoreForTeamMember(String memberListId, String memberPath) {
        try {
            if (StrUtil.isBlank(memberListId) || StrUtil.isBlank(memberPath)) {
                return BigDecimal.ZERO;
            }

            // 使用Mapper直接查询影响力分数
            BigDecimal influenceScore = memberListMapper.calculateInfluenceScoreForTeamMember(memberPath);
            return influenceScore != null ? influenceScore.setScale(2, BigDecimal.ROUND_HALF_UP) : BigDecimal.ZERO;

        } catch (Exception e) {
            log.error("计算团队成员影响力分数失败，memberListId: {}, memberPath: {}", memberListId, memberPath, e);
            return BigDecimal.ZERO;
        }
    }

    @Override
    public BigDecimal getMemberValidStoreSubsidyAmount(String memberId) {
        // 构建查询条件：
        // 1. 会员ID等于指定会员
        // 2. 交易类型为店铺补助金(50)
        // 3. 收入类型(0)
        // 4. 店铺补助金状态为可用(0)
        // 5. 未删除
        LambdaQueryWrapper<MemberAccountCapital> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MemberAccountCapital::getMemberListId, memberId)
                    .eq(MemberAccountCapital::getPayType, "50")
                    .eq(MemberAccountCapital::getGoAndCome, "0")
                    .eq(MemberAccountCapital::getStoreSubsidyStatus, "0")
                    .eq(MemberAccountCapital::getDelFlag, "0");
        
        // 查询所有符合条件的记录
        List<MemberAccountCapital> subsidyRecords = iMemberAccountCapitalService.list(queryWrapper);
        
        // 计算总金额 - 使用remainingAmount字段而非amount
        BigDecimal totalSubsidyAmount = BigDecimal.ZERO;
        if (subsidyRecords != null && !subsidyRecords.isEmpty()) {
            for (MemberAccountCapital record : subsidyRecords) {
                BigDecimal remainingAmount = record.getRemainingAmount();
                if (remainingAmount != null && remainingAmount.compareTo(BigDecimal.ZERO) > 0) {
                    totalSubsidyAmount = totalSubsidyAmount.add(remainingAmount);
                }
            }
        }
        
        // 返回汇总金额，保留2位小数
        return totalSubsidyAmount.setScale(2, BigDecimal.ROUND_HALF_UP);
    }
    
    @Override
    public BigDecimal getMemberAvailableStoreSubsidyAmount(String memberId) {
        // 构建查询条件：
        // 1. 会员ID等于指定会员
        // 2. 交易类型为店铺补助金(50)
        // 3. 收入类型(0)
        // 4. 店铺补助金状态为可用(0)
        // 5. 未过期 (expire_time > now)
        // 6. 剩余金额大于0
        // 7. 未删除
        LambdaQueryWrapper<MemberAccountCapital> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MemberAccountCapital::getMemberListId, memberId)
                    .eq(MemberAccountCapital::getPayType, "50")
                    .eq(MemberAccountCapital::getGoAndCome, "0")
                    .eq(MemberAccountCapital::getStoreSubsidyStatus, "0")
                    .gt(MemberAccountCapital::getExpireTime, new Date())
                    .gt(MemberAccountCapital::getRemainingAmount, BigDecimal.ZERO)
                    .eq(MemberAccountCapital::getDelFlag, "0");
        
        // 查询所有符合条件的记录
        List<MemberAccountCapital> subsidyRecords = iMemberAccountCapitalService.list(queryWrapper);
        
        // 计算总金额 - 使用remainingAmount字段
        BigDecimal totalAvailableAmount = BigDecimal.ZERO;
        if (subsidyRecords != null && !subsidyRecords.isEmpty()) {
            for (MemberAccountCapital record : subsidyRecords) {
                BigDecimal remainingAmount = record.getRemainingAmount();
                if (remainingAmount != null && remainingAmount.compareTo(BigDecimal.ZERO) > 0) {
                    totalAvailableAmount = totalAvailableAmount.add(remainingAmount);
                }
            }
        }
        
        // 返回汇总金额，保留2位小数
        return totalAvailableAmount.setScale(2, BigDecimal.ROUND_HALF_UP);
    }
    
    @Override
    public BigDecimal getMemberExpiredStoreSubsidyAmount(String memberId) {
        // 构建查询条件：
        // 1. 会员ID等于指定会员
        // 2. 交易类型为店铺补助金(50)
        // 3. 收入类型(0)
        // 4. 店铺补助金状态为已过期(1)
        // 5. 未删除
        LambdaQueryWrapper<MemberAccountCapital> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MemberAccountCapital::getMemberListId, memberId)
                    .eq(MemberAccountCapital::getPayType, "50")
                    .eq(MemberAccountCapital::getGoAndCome, "0")
                    .eq(MemberAccountCapital::getStoreSubsidyStatus, "1")
                    .eq(MemberAccountCapital::getDelFlag, "0");
        
        // 查询所有符合条件的记录
        List<MemberAccountCapital> expiredRecords = iMemberAccountCapitalService.list(queryWrapper);
        
        // 计算总金额 - 使用amount字段（初始发放金额）
        BigDecimal totalExpiredAmount = BigDecimal.ZERO;
        if (expiredRecords != null && !expiredRecords.isEmpty()) {
            for (MemberAccountCapital record : expiredRecords) {
                BigDecimal amount = record.getAmount();
                if (amount != null && amount.compareTo(BigDecimal.ZERO) > 0) {
                    totalExpiredAmount = totalExpiredAmount.add(amount);
                }
            }
        }
        
        // 返回汇总金额，保留2位小数
        return totalExpiredAmount.setScale(2, BigDecimal.ROUND_HALF_UP);
    }
    
    @Override
    @Transactional
    public BigDecimal subtractStoreSubsidy(String memberId, String storeId, BigDecimal amount, String orderNo, String payType) {
        log.info("开始扣除会员[{}]的店铺[{}]专用补助金，金额：{}，订单号：{}", memberId, storeId, amount, orderNo);
        
        // 如果金额为零，直接返回
        if (amount == null || amount.compareTo(BigDecimal.ZERO) <= 0) {
            return BigDecimal.ZERO;
        }
        
        // 构建查询条件：
        // 1. 会员ID等于指定会员
        // 2. 店铺ID等于指定店铺
        // 3. 交易类型为店铺补助金(50)
        // 4. 收入类型(0)
        // 5. 店铺补助金状态为可用(0)
        // 6. 未删除
        // 7. 按发放时间升序排序（优先扣除早期发放的补助金）
        LambdaQueryWrapper<MemberAccountCapital> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MemberAccountCapital::getMemberListId, memberId)
                    .eq(MemberAccountCapital::getSubsidyStoreId, storeId)
                    .eq(MemberAccountCapital::getPayType, "50")
                    .eq(MemberAccountCapital::getGoAndCome, "0")
                    .eq(MemberAccountCapital::getStoreSubsidyStatus, "0")
                    .eq(MemberAccountCapital::getDelFlag, "0")
                    .gt(MemberAccountCapital::getRemainingAmount, 0)
                    .orderByAsc(MemberAccountCapital::getCreateTime);
        
        List<MemberAccountCapital> subsidyRecords = iMemberAccountCapitalService.list(queryWrapper);
        
        // 如果没有可用的店铺补助金记录，返回零
        if (subsidyRecords == null || subsidyRecords.isEmpty()) {
            log.info("会员[{}]没有店铺[{}]的可用补助金", memberId, storeId);
            return BigDecimal.ZERO;
        }
        
        BigDecimal remainingAmount = amount; // 剩余需要扣除的金额
        BigDecimal actualSubtracted = BigDecimal.ZERO; // 实际扣除的补助金总额
        
        // 遍历所有补助金记录，优先使用较早发放的补助金
        for (MemberAccountCapital record : subsidyRecords) {
            if (remainingAmount.compareTo(BigDecimal.ZERO) <= 0) {
                break; // 如果已经扣除完毕，退出循环
            }
            
            BigDecimal currentRemaining = record.getRemainingAmount();
            
            if (currentRemaining.compareTo(remainingAmount) >= 0) {
                // 当前补助金记录足够支付剩余金额
                record.setRemainingAmount(currentRemaining.subtract(remainingAmount));
                actualSubtracted = actualSubtracted.add(remainingAmount);
                remainingAmount = BigDecimal.ZERO;
            } else {
                // 当前补助金记录不足以支付剩余金额
                record.setRemainingAmount(BigDecimal.ZERO);
                actualSubtracted = actualSubtracted.add(currentRemaining);
                remainingAmount = remainingAmount.subtract(currentRemaining);
            }
            
            // 更新补助金记录的状态
            if (record.getRemainingAmount().compareTo(BigDecimal.ZERO) == 0) {
                // 如果剩余金额为0，更新状态为已用尽
                record.setStoreSubsidyStatus("2");
            }
            iMemberAccountCapitalService.updateById(record);
            
            // 创建店铺补助金消费/抵扣的支出记录
            BigDecimal consumedAmount = actualSubtracted;
            
            MemberAccountCapital outRecord = new MemberAccountCapital();
            outRecord.setMemberListId(memberId);
            outRecord.setAmount(consumedAmount);
            outRecord.setRemainingAmount(record.getRemainingAmount());
            outRecord.setOrderNo(orderNo);
            outRecord.setGoAndCome("1"); // 支出
            outRecord.setPayType(payType); // 使用传入的payType，应该是'51'
            outRecord.setRemarks("订单使用" + storeId + "店铺补助金抵扣，原补助金记录ID: " + record.getId());
            outRecord.setSubsidyStoreId(storeId);
            outRecord.setStoreSubsidyStatus("0"); // 支出记录状态设为0
            outRecord.setTradeNo(orderNo + "_subsidy_" + System.currentTimeMillis()); // 生成唯一交易流水号
            
            // 获取当前会员的通用账户余额（不包含补助金）
            MemberList memberList = this.getById(memberId);
            if (memberList != null) {
                outRecord.setBalance(memberList.getBalance()); // 通用余额不因补助金抵扣而改变
            }
            
            iMemberAccountCapitalService.save(outRecord);
        }
        
        log.info("会员[{}]实际扣除店铺[{}]专用补助金：{}", memberId, storeId, actualSubtracted);
        return actualSubtracted.setScale(2, BigDecimal.ROUND_HALF_UP);
    }
    
    @Override
    public Map<String, Object> getMemberStoreSubsidyInfo(String memberId, String storeId) {
        log.info("查询会员[{}]在店铺[{}]的补助金信息", memberId, storeId);
        
        Map<String, Object> result = new HashMap<>();
        
        // 构建查询条件：
        // 1. 会员ID等于指定会员
        // 2. 店铺ID等于指定店铺
        // 3. 交易类型为店铺补助金(50)
        // 4. 收入类型(0)
        // 5. 店铺补助金状态为可用(0)
        // 6. 未过期 (expire_time > now)
        // 7. 剩余金额大于0
        // 8. 未删除
        LambdaQueryWrapper<MemberAccountCapital> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MemberAccountCapital::getMemberListId, memberId)
                    .eq(MemberAccountCapital::getSubsidyStoreId, storeId)
                    .eq(MemberAccountCapital::getPayType, "50")
                    .eq(MemberAccountCapital::getGoAndCome, "0")
                    .eq(MemberAccountCapital::getStoreSubsidyStatus, "0")
                    .gt(MemberAccountCapital::getExpireTime, new Date())
                    .gt(MemberAccountCapital::getRemainingAmount, BigDecimal.ZERO)
                    .eq(MemberAccountCapital::getDelFlag, "0")
                    .orderByAsc(MemberAccountCapital::getExpireTime);
        
        // 查询所有符合条件的记录
        List<MemberAccountCapital> subsidyRecords = iMemberAccountCapitalService.list(queryWrapper);
        
        BigDecimal totalAmount = BigDecimal.ZERO;
        String nearestExpireTime = "";
        
        if (subsidyRecords != null && !subsidyRecords.isEmpty()) {
            // 计算总金额
            for (MemberAccountCapital record : subsidyRecords) {
                BigDecimal remainingAmount = record.getRemainingAmount();
                if (remainingAmount != null && remainingAmount.compareTo(BigDecimal.ZERO) > 0) {
                    totalAmount = totalAmount.add(remainingAmount);
                }
            }
            
            // 获取最近过期时间（第一条记录，因为已按过期时间升序排序）
            Date nearestExpireDate = subsidyRecords.get(0).getExpireTime();
            if (nearestExpireDate != null) {
                nearestExpireTime = DateUtils.formatDate(nearestExpireDate, "yyyy-MM-dd");
            }
        }
        
        result.put("totalAmount", totalAmount.setScale(2, BigDecimal.ROUND_HALF_UP));
        result.put("nearestExpireTime", nearestExpireTime);
        
        log.info("会员[{}]在店铺[{}]的补助金信息：总金额={}, 最近过期时间={}", 
                memberId, storeId, totalAmount, nearestExpireTime);
        
        return result;
    }

    @Override
    @Transactional
    public BigDecimal restoreStoreSubsidyForRefund(String memberId, String orderNo) {
        log.info("开始处理订单[{}]的店铺补助金退款恢复，会员ID：{}", orderNo, memberId);
        
        if (StringUtils.isBlank(memberId) || StringUtils.isBlank(orderNo)) {
            log.warn("会员ID或订单号为空，无法处理店铺补助金退款");
            return BigDecimal.ZERO;
        }
        
        // 1. 查找该订单的所有店铺补助金消费记录（pay_type='51'）
        LambdaQueryWrapper<MemberAccountCapital> consumeQueryWrapper = new LambdaQueryWrapper<>();
        consumeQueryWrapper.eq(MemberAccountCapital::getMemberListId, memberId)
                          .eq(MemberAccountCapital::getOrderNo, orderNo)
                          .eq(MemberAccountCapital::getPayType, "51")  // 店铺补助金消费/抵扣
                          .eq(MemberAccountCapital::getGoAndCome, "1")  // 支出
                          .eq(MemberAccountCapital::getDelFlag, "0");
        
        List<MemberAccountCapital> consumeRecords = iMemberAccountCapitalService.list(consumeQueryWrapper);
        
        if (consumeRecords == null || consumeRecords.isEmpty()) {
            log.info("订单[{}]没有使用店铺补助金，无需恢复", orderNo);
            return BigDecimal.ZERO;
        }
        
        BigDecimal totalRestoredAmount = BigDecimal.ZERO;
        
        // 2. 遍历每条消费记录，恢复到原补助金发放记录
        for (MemberAccountCapital consumeRecord : consumeRecords) {
            try {
                BigDecimal consumeAmount = consumeRecord.getAmount();
                String storeId = consumeRecord.getSubsidyStoreId();
                String remarks = consumeRecord.getRemarks();
                
                log.info("处理店铺补助金消费记录：金额={}，店铺ID={}，备注={}", consumeAmount, storeId, remarks);
                
                // 从备注中提取原补助金记录ID
                String originalSubsidyId = extractOriginalSubsidyId(remarks);
                
                if (StringUtils.isNotBlank(originalSubsidyId)) {
                    // 3. 查找原补助金发放记录
                    MemberAccountCapital originalSubsidy = iMemberAccountCapitalService.getById(originalSubsidyId);
                    
                    if (originalSubsidy != null && "50".equals(originalSubsidy.getPayType()) && "0".equals(originalSubsidy.getGoAndCome())) {
                        // 4. 检查原补助金是否未过期且状态允许恢复
                        Date now = new Date();
                        if (originalSubsidy.getExpireTime() != null && originalSubsidy.getExpireTime().after(now) 
                            && !"1".equals(originalSubsidy.getStoreSubsidyStatus())) {
                            
                            // 5. 恢复原补助金记录的remaining_amount
                            BigDecimal currentRemaining = originalSubsidy.getRemainingAmount() != null ? 
                                                        originalSubsidy.getRemainingAmount() : BigDecimal.ZERO;
                            BigDecimal newRemaining = currentRemaining.add(consumeAmount);
                            
                            originalSubsidy.setRemainingAmount(newRemaining);
                            
                            // 6. 更新状态：如果恢复后金额大于0且原状态为已用尽，则改为可用
                            if (newRemaining.compareTo(BigDecimal.ZERO) > 0 && "2".equals(originalSubsidy.getStoreSubsidyStatus())) {
                                originalSubsidy.setStoreSubsidyStatus("0");  // 改为可用
                            }
                            
                            iMemberAccountCapitalService.updateById(originalSubsidy);
                            
                            // 7. 生成店铺补助金退回/恢复流水记录（pay_type='52'）
                            MemberAccountCapital restoreRecord = new MemberAccountCapital();
                            restoreRecord.setMemberListId(memberId);
                            restoreRecord.setPayType("52");  // 店铺补助金退回/恢复
                            restoreRecord.setGoAndCome("0");  // 收入
                            restoreRecord.setAmount(consumeAmount);
                            restoreRecord.setOrderNo(orderNo);
                            restoreRecord.setTradeNo(orderNo + "_restore_" + System.currentTimeMillis());
                            restoreRecord.setSubsidyStoreId(storeId);
                            restoreRecord.setStoreSubsidyStatus("0");
                            restoreRecord.setRemarks("订单[" + orderNo + "]退款恢复店铺补助金，原补助金记录ID: " + originalSubsidyId);
                            
                            // 获取当前会员的通用账户余额（不包含补助金）
                            MemberList memberList = this.getById(memberId);
                            if (memberList != null) {
                                restoreRecord.setBalance(memberList.getBalance());  // 通用余额不因补助金恢复而改变
                            }
                            
                            iMemberAccountCapitalService.save(restoreRecord);
                            
                            totalRestoredAmount = totalRestoredAmount.add(consumeAmount);
                            
                            log.info("成功恢复店铺补助金：原记录ID={}，恢复金额={}，新剩余金额={}", 
                                   originalSubsidyId, consumeAmount, newRemaining);
                        } else {
                            log.warn("原补助金记录已过期或状态不允许恢复：记录ID={}，过期时间={}，状态={}", 
                                   originalSubsidyId, originalSubsidy.getExpireTime(), originalSubsidy.getStoreSubsidyStatus());
                        }
                    } else {
                        log.warn("找不到有效的原补助金发放记录：记录ID={}", originalSubsidyId);
                    }
                } else {
                    log.warn("无法从备注中提取原补助金记录ID：{}", remarks);
                }
                
            } catch (Exception e) {
                log.error("处理店铺补助金消费记录失败：记录ID={}", consumeRecord.getId(), e);
                // 继续处理其他记录，不中断整个流程
            }
        }
        
        log.info("订单[{}]店铺补助金退款恢复完成，总恢复金额：{}", orderNo, totalRestoredAmount);
        return totalRestoredAmount.setScale(2, BigDecimal.ROUND_HALF_UP);
    }
    
    /**
     * 从备注中提取原补助金记录ID
     * 备注格式：订单使用{storeId}店铺补助金抵扣，原补助金记录ID: {originalId}
     */
    private String extractOriginalSubsidyId(String remarks) {
        if (StringUtils.isBlank(remarks)) {
            return null;
        }
        
        // 使用正则表达式提取原补助金记录ID
        String pattern = "原补助金记录ID:\\s*([a-zA-Z0-9\\-]+)";
        java.util.regex.Pattern regex = java.util.regex.Pattern.compile(pattern);
        java.util.regex.Matcher matcher = regex.matcher(remarks);
        
        if (matcher.find()) {
            return matcher.group(1);
        }
        
        return null;
    }

    @Override
    public IPage<Map<String, Object>> getEmployeePerformanceRanking(Page<Map<String, Object>> page) {
        return baseMapper.getEmployeePerformanceRanking(page);
    }

    /**
     * 更新打榜者业绩
     * @param memberId 会员ID
     * @param performance 业绩金额
     */
    @Override
    public void updateRankerPerformance(String memberId, BigDecimal performance) {
        MemberList member = this.getById(memberId);
        if (member != null && "1".equals(member.getIsRanker())) {
            BigDecimal currentPerformance = member.getRankerPerformance() != null ?
                    member.getRankerPerformance() : BigDecimal.ZERO;
            member.setRankerPerformance(currentPerformance.add(performance));
            this.updateById(member);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deactivateAccount(String memberId, String reason) {
        log.info("开始执行账户注销，memberId: {}, reason: {}", memberId, reason);

        try {
            // 1. 获取用户信息
            MemberList member = this.getById(memberId);
            if (member == null) {
                throw new RuntimeException("用户不存在");
            }

            // 2. 检查当前状态
            if ("1".equals(member.getIsDeactivated())) {
                throw new RuntimeException("账户已注销");
            }

            // 3. 更新用户状态
            MemberList updateMember = new MemberList();
            updateMember.setId(memberId);
            updateMember.setStatus("0"); // 停用状态
            updateMember.setIsDeactivated("1"); // 已注销
            updateMember.setDeactivatedTime(new Date());
            updateMember.setDeactivatedReason(reason);
            updateMember.setUpdateTime(new Date());

            boolean updateResult = this.updateById(updateMember);
            if (!updateResult) {
                throw new RuntimeException("更新用户状态失败");
            }

            // 4. 清理相关缓存（如果有的话）
            // 这里可以添加清理Redis缓存的逻辑

            log.info("账户注销成功，memberId: {}, reason: {}", memberId, reason);

        } catch (Exception e) {
            log.error("账户注销失败，memberId: " + memberId, e);
            throw new RuntimeException("注销失败：" + e.getMessage());
        }
    }
}
