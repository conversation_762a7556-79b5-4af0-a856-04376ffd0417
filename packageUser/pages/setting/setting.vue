<template>
	<view class="setting">
	<!-- 	<view class="setting-wrap" v-if="users.token">
			<view class="setting-line" @click="authPhone(1)">
				<view>
					交易密码
				</view>
				<view>
					<view style="color: #A2A2A2;">
						{{ users.userInfo.transactionPassword == 1 ? '已' : '未' }}设置
					</view>
					<image src="@/static/right-arrow-black.png" mode=""></image>
				</view>
			</view>

		</view> -->
		<view class="setting-wrap">
			<view class="setting-line" @click="navTo('/packageSearch/pages/agreement/agreement?agreementCode=USER_SERVICE_AGREEMENT')">
				<view>
					服务条款
				</view>
				<view>
					<image src="@/static/right-arrow-black.png" mode=""></image>
				</view>
			</view>
			<view class="setting-line" @click="navTo('/packageSearch/pages/agreement/agreement?agreementCode=PRIVACY_POLICY')">
				<view>
					隐私协议
				</view>
				<view>
					<image src="@/static/right-arrow-black.png" mode=""></image>
				</view>
			</view>
		</view>

		<!-- 注销账户功能区域 -->
		<view class="setting-wrap" v-if="users.token">
			<view class="setting-line setting-line-danger" @click="showDeactivateConfirm">
				<view class="danger-text">
					⚠️ 注销账户
				</view>
				<view>
					<image src="@/static/right-arrow-black.png" mode=""></image>
				</view>
			</view>
		</view>
		<!-- <view class="setting-wrap">
			<view class="setting-line">
				<view>
					软件版本
				</view>
				<view>
					当前版本V{{version}}
				</view>
			</view>
		</view> -->
		<template v-if="users.token">

			<view class="setting-btn">
				<view @click="showLogoutPop">
					退出登录
				</view>
			</view>
		</template>

		<!-- 退出登录弹窗 -->
		<uni-popup ref="popup" type="dialog">
			<uni-popup-dialog type='info' mode="base" title='温馨提示' content="您确定要退出登录吗?" :duration="2000"
				:before-close="false" @confirm="userLogout"></uni-popup-dialog>
		</uni-popup>

		<!-- 注销账户风险提示弹窗 -->
		<uni-popup ref="deactivatePopup" type="dialog">
			<uni-popup-dialog
				type='warn'
				mode="base"
				title='⚠️ 注销账户风险提示'
				:content="deactivateWarningText"
				:duration="2000"
				:before-close="false"
				@confirm="showFinalConfirm"
				@close="cancelDeactivate">
			</uni-popup-dialog>
		</uni-popup>

		<!-- 最终确认弹窗 -->
		<uni-popup ref="finalConfirmPopup" type="center">
			<view class="final-confirm-dialog">
				<view class="dialog-title">🔒 确认注销账户</view>
				<view class="dialog-content">
					<text class="warning-text">此操作不可撤销，请输入您的手机号确认：</text>
					<input
						v-model="confirmPhone"
						placeholder="请输入手机号"
						class="confirm-input"
						type="number"
						maxlength="11"
					/>
				</view>
				<view class="dialog-actions">
					<button @click="cancelFinalConfirm" class="cancel-btn">取消</button>
					<button @click="confirmDeactivate" class="confirm-btn" :disabled="!isPhoneValid">确认注销</button>
				</view>
			</view>
		</uni-popup>
	</view>
</template>
<script setup>
	import {
		userStore
	} from '@/store';
	import {
		getSafeBottom
	} from '@/utils';
	import {
		navToBeforeLogin,
		navTo,
		toUpPage,
		authPhone
	} from '@/hooks'
	import {
		ref,
		computed
	} from "vue";
	const popup = ref()
	const deactivatePopup = ref()
	const finalConfirmPopup = ref()
	const confirmPhone = ref('')
	const users = userStore()

	// 注销风险提示文本
	const deactivateWarningText = `注销后将发生以下情况：
• 账户将被永久停用，无法恢复
• 所有个人数据将被清除
• 剩余余额需提前处理
• 无法再使用此手机号注册

请确认您已了解上述风险！`

	// 验证手机号是否正确
	const isPhoneValid = computed(() => {
		return confirmPhone.value === users.userInfo?.phone;
	})

	//显示退出登录弹窗提示
	function showLogoutPop() {
		popup.value.open()
	}
	//退出登录
	function userLogout() {
		users.logOut()
		uni.showToast({
			title: '退出登录成功~',
			icon: 'none'
		})
		setTimeout(() => {
			toUpPage()
		}, 500)
	}

	// 显示注销确认弹窗
	function showDeactivateConfirm() {
		deactivatePopup.value.open();
	}

	// 取消注销
	function cancelDeactivate() {
		deactivatePopup.value.close();
	}

	// 显示最终确认弹窗
	function showFinalConfirm() {
		deactivatePopup.value.close();
		confirmPhone.value = '';
		finalConfirmPopup.value.open();
	}

	// 取消最终确认
	function cancelFinalConfirm() {
		finalConfirmPopup.value.close();
		confirmPhone.value = '';
	}

	// 确认注销账户
	async function confirmDeactivate() {
		if (!isPhoneValid.value) {
			uni.showToast({
				title: '手机号输入错误',
				icon: 'none'
			});
			return;
		}

		try {
			uni.showLoading({ title: '注销中...', mask: true });

			const { data } = await uni.http.post(uni.api.deactivateAccount, {
				reason: '用户主动注销',
				confirmPhone: confirmPhone.value,
				userConfirmed: true
			}, {
				header: {
					'Content-Type': 'application/json'
				}
			});

			uni.hideLoading();

			if (data.success) {
				uni.showToast({
					title: '账户注销成功',
					icon: 'success'
				});

				// 清除本地数据并跳转登录页
				setTimeout(() => {
					users.logOut();
					uni.reLaunch({
						url: '/pages/login/login'
					});
				}, 1500);
			} else {
				uni.showToast({
					title: data.message || '注销失败',
					icon: 'none'
				});
			}
		} catch (error) {
			uni.hideLoading();
			console.error('注销失败:', error);
			uni.showToast({
				title: '注销失败，请重试',
				icon: 'none'
			});
		} finally {
			finalConfirmPopup.value.close();
			confirmPhone.value = '';
		}
	}
</script>
<style lang="scss">
	page {
		background-color: #f8f8f8;
	}

	.setting {
		width: 750rpx;
		padding: 32rpx;

		&-placeholder {
			padding-bottom: v-bind(getSafeBottom());
		}

		&-btnOpe {
			padding-bottom: v-bind(getSafeBottom());
		}


		&-btn {
			position: fixed;
			bottom: 0;
			left: 0;
			z-index: 5;
			width: 750rpx;
			padding: 30rpx;
			background-color: white;

			>view {
				height: 84rpx;
				line-height: 84rpx;
				text-align: center;
				color: white;
				font-size: 30rpx;
				background-color: #22A3FF;
				border-radius: 16rpx;
			}
		}


		&-wrap {
			background: #FFFFFF;
			border-radius: 24rpx;
			padding: 0 32rpx;
			margin-bottom: 24rpx;
		}

		>view:last-child {
			margin-bottom: 0;
		}

		&-line {
			width: 100%;
			height: 120rpx;
			display: flex;
			align-items: center;
			justify-content: space-between;
			font-size: 30rpx;
			color: #000000;

			>view:nth-child(2) {
				display: flex;
				align-items: center;
				justify-content: flex-end;

				image {
					width: 32rpx;
					height: 32rpx;
					margin-left: 8rpx;
				}
			}

			// 注销账户危险样式
			&-danger {
				.danger-text {
					color: #FF6B35;
					font-weight: 600;
				}
			}
		}
	}

	// 最终确认弹窗样式
	.final-confirm-dialog {
		width: 600rpx;
		padding: 40rpx;
		background: white;
		border-radius: 20rpx;

		.dialog-title {
			font-size: 36rpx;
			font-weight: bold;
			text-align: center;
			margin-bottom: 30rpx;
			color: #333;
		}

		.dialog-content {
			margin-bottom: 40rpx;

			.warning-text {
				font-size: 28rpx;
				color: #666;
				line-height: 1.5;
				display: block;
				margin-bottom: 20rpx;
			}

			.confirm-input {
				width: 100%;
				height: 80rpx;
				border: 2rpx solid #ddd;
				border-radius: 10rpx;
				padding: 0 20rpx;
				font-size: 30rpx;
				box-sizing: border-box;
			}
		}

		.dialog-actions {
			display: flex;
			gap: 20rpx;

			button {
				flex: 1;
				height: 80rpx;
				border-radius: 10rpx;
				font-size: 30rpx;
				border: none;
			}

			.cancel-btn {
				background: #f5f5f5;
				color: #666;
			}

			.confirm-btn {
				background: #FF6B35;
				color: white;

				&:disabled {
					background: #ccc;
					color: #999;
				}
			}
		}
	}
</style>
