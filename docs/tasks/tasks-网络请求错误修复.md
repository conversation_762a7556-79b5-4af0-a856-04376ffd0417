# 八闽助业集市网络请求错误修复任务计划

## 📋 任务概述

**任务名称**: 网络请求错误修复项目  
**创建时间**: 2025-01-31  
**执行方案**: 方案1 - 保守修复方案  
**预计完成时间**: 2小时  

## 🎯 问题描述

### 问题1：管理后台审核功能接口参数错误
- **文件位置**: `/Users/<USER>/Dev/SideWork/heartful-mall/heartful-mall-web/src/views/rankerApplication/modules/RankerApplicationAuditModal.vue`
- **错误现象**: 点击"审核通过"、"审核拒绝"操作时，后端接口报错缺少必需的请求参数'applicationId'
- **错误详情**: `org.springframework.web.bind.MissingServletRequestParameterException: Required request parameter 'applicationId' for method parameter type String is not present`
- **根本原因**: 使用httpAction方法发送JSON格式数据，但后端接口使用@RequestParam注解需要表单格式数据

### 问题2：C端用户小程序注销账户接口Content-Type错误
- **文件位置**: `packageUser/pages/setting/setting.vue`
- **错误现象**: 用户端小程序注销账户时接口报错，不支持'application/x-www-form-urlencoded'内容类型
- **错误详情**: `org.springframework.web.HttpMediaTypeNotSupportedException: Content type 'application/x-www-form-urlencoded;charset=UTF-8' not supported`
- **根本原因**: uni-app默认使用application/x-www-form-urlencoded，但后端接口使用@RequestBody注解需要JSON格式数据

## 🔧 修复方案

### 方案选择：保守修复方案
- **核心理念**: 最小化改动，精准修复，风险可控
- **修复原则**: 只修改出问题的具体代码，不影响其他功能
- **技术依据**: 严格遵循项目开发规范文档

## 📝 详细实施计划

### 阶段1：问题1修复 - 管理后台审核功能接口参数错误

#### 1.1 分析当前代码问题
- **目标**: 查看RankerApplicationAuditModal.vue的当前代码实现
- **重点**: 确认httpAction调用的具体位置和参数结构
- **预期结果**: 明确需要修改的代码行数和参数格式

#### 1.2 修改API调用方法
- **修改内容**: 将httpAction改为postApplicationAction
- **修改位置**: 第217行左右
- **参数调整**: 保持现有参数结构不变
- **技术原理**: postApplicationAction使用application/x-www-form-urlencoded格式，符合@RequestParam注解要求

#### 1.3 验证修复效果
- **测试内容**: 审核通过和审核拒绝功能
- **验证标准**: 不再报错，能正常提交审核结果
- **回滚准备**: 如有问题立即回滚到原始代码

### 阶段2：问题2修复 - C端用户小程序注销账户接口Content-Type错误

#### 2.1 分析当前请求配置
- **目标**: 查看packageUser/pages/setting/setting.vue中注销账户的当前请求实现
- **重点**: 确认uni.http.post调用的具体位置和当前配置
- **预期结果**: 明确需要添加Content-Type设置的位置

#### 2.2 修改Content-Type设置
- **修改内容**: 为uni.http.post调用添加正确的Content-Type: application/json设置
- **修改位置**: 第197行左右
- **技术原理**: 后端@RequestBody注解需要JSON格式数据

#### 2.3 验证修复效果
- **测试内容**: 注销账户功能
- **验证标准**: 不再报Content-Type错误，能正常注销账户
- **回滚准备**: 如有问题立即回滚到原始代码

### 阶段3：测试验证与文档记录

#### 3.1 功能测试
- **测试范围**: 修复后的审核功能和注销功能
- **测试方法**: 手动测试 + 功能验证
- **测试标准**: 所有功能正常工作，无报错

#### 3.2 创建修复文档
- **文档名称**: tasks-网络请求错误修复.md
- **文档内容**: 修复过程、技术原理、最佳实践
- **存放位置**: docs/tasks/目录

#### 3.3 总结与建议
- **经验总结**: 记录修复过程中的关键发现
- **预防建议**: 提出防止类似问题的措施
- **最佳实践**: 整理网络请求开发的最佳实践

## 🎯 成功标准

1. **功能完整性**: 审核功能和注销功能正常工作
2. **错误消除**: 不再出现原有的错误信息
3. **代码质量**: 修复后的代码符合项目规范
4. **文档完整**: 完整记录修复过程和经验

## ⚠️ 风险控制

1. **备份策略**: 修改前备份原始文件
2. **测试策略**: 每个修改后立即测试
3. **回滚策略**: 如有问题立即回滚
4. **影响范围**: 仅修改出问题的具体功能

## 📚 参考文档

- `/Users/<USER>/Dev/SideWork/heartful-mall/docs/开发规范/后台管理系统网络请求最佳实践规范.md`
- `/Users/<USER>/Dev/SideWork/heartful-mall/docs/开发规范/C端用户小程序网络请求开发规范.md`
- `/Users/<USER>/Dev/SideWork/heartful-mall/docs/开发规范/C端用户小程序API规范.md`

## 🚀 执行准备

- [x] 任务计划制定完成
- [ ] 开始执行阶段1.1：分析当前代码问题
- [ ] 准备开发环境和测试环境
- [ ] 确认相关文档和规范

---

**注意**: 本计划严格遵循八闽助业集市项目的开发规范，采用保守修复方案确保风险可控。
