<template>
  <a-row :gutter="10">
    <a-col :span="4" v-if="userRole !== 'Merchant'">
      <a-card title="店铺列表" :bordered="false" style="min-height: 1000px">
        <store-tree @ok="getSelectStore"></store-tree>
      </a-card>
    </a-col>
    <a-col :span="4">
      <a-card title="分类列表" :bordered="false" style="min-height: 1000px">
        <div v-if="goodTypeTree.length == 0 && storeInfo.key === 'ALL_STORES'">
          <a-icon type="info-circle" style="color: #1890ff; margin-right: 8px" />
          已选择全部店铺，显示所有商品
        </div>
        <div v-if="goodTypeTree.length == 0 && storeInfo.key && storeInfo.key !== 'ALL_STORES'">
          <a-icon type="check-circle" style="color: #52c41a; margin-right: 8px" />
          已显示该店铺所有商品，可选择分类进一步筛选
        </div>
        <div v-if="goodTypeTree.length == 0 && !storeInfo.key">请选择左侧店铺信息</div>
        <a-tree v-if="goodTypeTree.length > 0" :tree-data="goodTypeTree">
          <template #title="{ name, id, level }">
            <span v-if="id === ''" style="color: #1890ff"
              ><div @click="getByGoodType(id, level)">{{ name }}</div></span
            >
            <template v-else
              ><div @click="getByGoodType(id, level)">{{ name }}</div></template
            >
          </template>
        </a-tree>
      </a-card>
    </a-col>
    <a-col :span="16">
      <a-card :bordered="false" style="min-height: 800px">
        <!-- 查询区域 -->
        <div class="table-page-search-wrapper">
          <a-form layout="inline" @keyup.enter.native="searchQuery">
            <a-row :gutter="24">
              <a-col :xl="6" :lg="7" :md="8" :sm="24">
                <a-form-item label="商品编号">
                  <a-input placeholder="请输入商品编号" v-model="queryParam.goodNo"></a-input>
                </a-form-item>
              </a-col>
              <a-col :xl="6" :lg="7" :md="8" :sm="24">
                <a-form-item label="商品名称">
                  <a-input placeholder="请输入商品名称" v-model="queryParam.goodName"></a-input>
                </a-form-item>
              </a-col>
              <a-col :xl="6" :lg="7" :md="8" :sm="24">
                <a-form-item label="状态">
                  <a-select v-model="queryParam.status" placeholder="请选择状态">
                    <a-select-option value="">全部</a-select-option>
                    <a-select-option value="0">停用</a-select-option>
                    <a-select-option value="1">启用</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :xl="6" :lg="7" :md="8" :sm="24">
                <a-form-item label="上下架状态">
                  <a-select v-model="queryParam.frameStatus" placeholder="请选择状态">
                    <a-select-option value="">全部</a-select-option>
                    <a-select-option value="0">下架</a-select-option>
                    <a-select-option value="1">上架</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :xl="6" :lg="7" :md="8" :sm="24">
                <a-form-item label="曝光商品">
                  <a-select v-model="queryParam.isExposureGood" placeholder="请选择曝光状态">
                    <a-select-option value="">全部</a-select-option>
                    <a-select-option value="0">否</a-select-option>
                    <a-select-option value="1">是</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :xl="6" :lg="7" :md="8" :sm="24">
                <span style="float: left; overflow: hidden" class="table-page-search-submitButtons">
                  <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
                  <a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置</a-button>
                  <!--  <a @click="handleToggleSearch" style="margin-left: 8px">
                  {{ toggleSearchStatus ? '收起' : '展开' }}
                  <a-icon :type="toggleSearchStatus ? 'up' : 'down'"/>
                </a>-->
                </span>
              </a-col>
            </a-row>
          </a-form>
        </div>

        <!-- 操作按钮区域 -->
        <div class="table-operator">
          <a-button @click="handleAdd" type="primary" icon="plus">新增</a-button>
          <a-button @click="BatchShelves('1')" type="primary" icon="plus">批量上架</a-button>
          <a-button @click="showModalFrameStatus1()" type="primary" icon="plus">批量下架</a-button>
          <a-dropdown>
            <a-menu slot="overlay">
              <a-menu-item key="1" @click="batchUpdateExposureStatus('1')">设置为曝光商品</a-menu-item>
              <a-menu-item key="2" @click="batchUpdateExposureStatus('0')">取消曝光商品</a-menu-item>
            </a-menu>
            <a-button type="primary" v-has="'goodStoreList:batchUpdateExposureStatus'" icon="setting">曝光设置 <a-icon type="down" /></a-button>
          </a-dropdown>
<!--          <a-button type="primary" icon="download"  :loading="exportLoading"  @click="handleExportXls('平台商品列表')">导出</a-button>-->
<!--          <a-upload-->
<!--            name="file"-->
<!--            :showUploadList="false"-->
<!--            :multiple="false"-->
<!--            :headers="tokenHeader"-->
<!--            :action="importExcelUrl"-->
<!--            @change="handleImportExcel"-->
<!--          >-->
<!--            <a-button type="primary" icon="import">导入</a-button>-->
<!--          </a-upload>-->
          <a-dropdown v-if="selectedRowKeys.length > 0">
            <a-menu slot="overlay">
              <a-menu-item key="1" @click="batchDel"><a-icon type="delete" />删除</a-menu-item>
            </a-menu>
            <a-button style="margin-left: 8px"> 批量操作 <a-icon type="down" /></a-button>
          </a-dropdown>
        </div>

        <!-- table区域-begin -->
        <div>
          <div class="ant-alert ant-alert-info" style="margin-bottom: 16px">
            <i class="anticon anticon-info-circle ant-alert-icon"></i> 已选择
            <a style="font-weight: 600">{{ selectedRowKeys.length }}</a
            >项
            <a style="margin-left: 24px" @click="onClearSelected">清空</a>
          </div>

          <a-table
            ref="table"
            size="middle"
            :scroll="{ x: 2000 }"
            bordered
            rowKey="id"
            :columns="columns"
            :dataSource="dataSource"
            :pagination="ipagination"
            :loading="loading"
            :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
            @change="handleTableChange"
          >
            <template slot="mainPicture" slot-scope="text, record, index">
              <img :preview="'mainPicture' + index" :src="getImg(text)" style="width: 40px; height: 40px" />
            </template>

            <template slot="isExposureGood" slot-scope="text">
              <a-tag v-if="text == '1'" color="green" style="color: #fff; background-color: #52c41a; border-color: #52c41a;">已曝光</a-tag>
              <a-tag v-else color="default" style="color: #666; background-color: #f5f5f5; border-color: #d9d9d9;">未曝光</a-tag>
            </template>

            <span slot="action" slot-scope="text, record">
              <a @click="changeSort(record)">排序</a>
              <a-divider type="vertical" />
              <a @click="handleEdit(record)">编辑</a>
              <a-divider type="vertical" />
              <a v-if="record.frameStatus == 0" @click="updateFrameStatus(record.id, '1')">上架</a>
              <a v-if="record.frameStatus == 1" @click="showModalFrameStatus(record.id, '0')">下架</a>
              <a-divider type="vertical" />
              <a v-if="record.isExposureGood == '0'" v-has="'goodStoreList:batchUpdateExposureStatus'" @click="updateExposureStatus(record.id, '1')">设置曝光</a>
              <a v-if="record.isExposureGood == '1'" v-has="'goodStoreList:batchUpdateExposureStatus'" @click="updateExposureStatus(record.id, '0')">取消曝光</a>
              <!-- 曝光排序按钮，仅对已曝光商品显示 -->
              <template v-if="record.isExposureGood == '1'">
                <a-divider type="vertical" />
                <a @click="handleExposureSort(record)" v-has="'goodStoreList:batchUpdateExposureStatus'">
                  <a-icon type="sort-ascending" />
                  曝光排序
                </a>
              </template>
              <a-divider type="vertical" />
              <a-dropdown>
                <a class="ant-dropdown-link">更多 <a-icon type="down" /></a>
                <a-menu slot="overlay">
                  <a-menu-item>
                    <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
                      <a>删除</a>
                    </a-popconfirm>
                  </a-menu-item>
                </a-menu>
              </a-dropdown>
            </span>
          </a-table>
        </div>
        <!-- table区域-end -->

        <!-- 表单区域 -->
        <good-store-list-modal ref="modalForm" @ok="modalFormOk"></good-store-list-modal>
        <good-price-modal ref="modalForm1" @ok="modalFormOk" />

        <!-- 曝光排序对话框 -->
        <a-modal
          title="设置曝光排序值"
          :visible="exposureSortVisible"
          @ok="handleExposureSortOk"
          @cancel="handleExposureSortCancel"
          :confirmLoading="exposureSortLoading"
        >
          <a-form-model :model="exposureSortForm" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
            <a-form-model-item label="商品名称">
              <span>{{ exposureSortForm.goodName }}</span>
            </a-form-model-item>
            <a-form-model-item label="当前排序值">
              <span>{{ exposureSortForm.currentSortValue || 999 }}</span>
            </a-form-model-item>
            <a-form-model-item label="新排序值" required>
              <a-input-number
                v-model="exposureSortForm.exposureSortValue"
                :min="1"
                :max="9999"
                placeholder="请输入排序值（数值越小优先级越高）"
                style="width: 100%"
              />
              <div style="color: #999; font-size: 12px; margin-top: 4px;">
                数值越小优先级越高，建议设置范围：1-999
              </div>
            </a-form-model-item>
          </a-form-model>
        </a-modal>
      </a-card>
    </a-col>
  </a-row>
</template>

<script>
import GoodStoreListModal from './modules/GoodStoreListModal'
import GoodPriceModal from './modules/GoodPriceModal'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import { getAction, postApplicationAction, putAction } from '@/api/manage'
import StoreTree from '../common/StoreTree/StoreTree'
import { filterObj, getUrlParams } from '@/utils/util'
import { USER_INFO } from '@/store/mutation-types'
import Vue from 'vue'
export default {
  name: 'GoodStoreListList',
  mixins: [JeecgListMixin],
  components: {
    GoodStoreListModal,
    GoodPriceModal,
    StoreTree,
  },
  data() {
    return {
      description: '店铺商品列表管理页面',
      searchValue: '',
      // 表头
      columns: [
        {
          title: '#',
          dataIndex: '',
          key: 'rowIndex',
          width: 60,
          align: 'center',
          customRender: function (t, r, index) {
            return parseInt(index) + 1
          },
        },
        {
          title: '店铺名称',
          align: 'center',
          dataIndex: 'storeName',
        },
        {
          title: '商品分类',
          align: 'center',
          dataIndex: 'goodStoreTypeNames',
        },
        {
          title: '商品主图',
          align: 'center',
          dataIndex: 'mainPicture',
          scopedSlots: { customRender: 'mainPicture' },
        },
        {
          title: '商品编号',
          align: 'center',
          dataIndex: 'goodNo',
          customRender(text) {
            if (!text) {
              return '-'
            }
            return text
          },
        },
        {
          title: '商品名称',
          align: 'center',
          dataIndex: 'goodName',
        },
        {
          title: '市场价',
          align: 'center',
          dataIndex: 'marketPrice',
        },
        {
          title: '销售价',
          align: 'center',
          dataIndex: 'minPrice',
          customRender(text, record) {
            if (record.minPrice == record.maxPrice) {
              return text
            } else {
              return text + '-' + record.maxPrice
            }
          },
        },
        {
          title: '库存',
          align: 'center',
          dataIndex: 'repertory'
        },
        {
          title: '曝光商品',
          align: 'center',
          dataIndex: 'isExposureGood',
          scopedSlots: { customRender: 'isExposureGood' },
        },
        {
          title: '曝光排序',
          align: 'center',
          dataIndex: 'exposureSortValue',
          width: 100,
          sorter: true,
          sortDirections: ['ascend', 'descend'],
          customRender: (text, record) => {
            if (record.isExposureGood === '1') {
              return text || 999
            }
            return '-'
          }
        },
        // {
        //   title: '会员价',
        //   align: 'center',
        //   dataIndex: 'minVipPrice',
        //   customRender(text, record) {
        //     if (record.minVipPrice == record.maxVipPrice) {
        //       return text
        //     } else {
        //       return text + '-' + record.maxVipPrice
        //     }
        //   },
        // },
        {
          title: '状态',
          align: 'center',
          dataIndex: 'status',
          customRender(text) {
            if (text == 0) {
              return '停用'
            }
            if (text == 1) {
              return '启用'
            }
          },
        },
        {
          title: '上下架状态',
          align: 'center',
          dataIndex: 'frameStatus',
          customRender(text) {
            if (text == 0) {
              return '下架'
            }
            if (text == 1) {
              return '上架'
            }
          },
        },
        {
          title: '审核状态',
          align: 'center',
          dataIndex: 'auditStatus',
          customRender(text) {
            if (text == 0) {
              return '草稿'
            }
            if (text == 1) {
              return '待审核'
            }
            if (text == 2) {
              return '审核通过'
            }
            if (text == 3) {
              return '审核不通过'
            }
          },
        },
        {
          title: '状态说明',
          align: 'center',
          dataIndex: 'statusExplain',
          customRender(text) {
            if (!text) {
              return '-'
            }
            return text
          },
        },
        {
          title: '有无规格',
          align: 'center',
          dataIndex: 'isSpecification',
          customRender(text) {
            if (text == 0) {
              return '无规格'
            }
            if (text == 1) {
              return '有规格'
            }
          },
        },
        // {
        //   title: '销量',
        //   align: 'center',
        //   dataIndex: 'salesVolume',
        // },
        {
          title: '创建者',
          align: 'center',
          dataIndex: 'createBy',
        },
        {
          title: '创建时间',
          align: 'center',
          dataIndex: 'createTime',
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          width: '150px',
          fixed: 'right',
          scopedSlots: { customRender: 'action' },
        },
      ],
      url: {
        getBySysUserId:"storeManage/storeManage/getBySysUserId",
        list: '/goodStoreList/goodStoreList/list',
        delete: '/goodStoreList/goodStoreList/delete',
        deleteBatch: '/goodStoreList/goodStoreList/deleteBatch',
        exportXlsUrl: 'goodStoreList/goodStoreList/exportXls',
        importExcelUrl: 'goodStoreList/goodStoreList/importExcel',
        getStoreGoodTypeByTree: 'GoodStoreType/goodStoreType/getStoreGoodTypeByTree',
        updateFrameStatus: '/goodStoreList/goodStoreList/updateFrameStatus',
        batchUpdateExposureStatus: '/goodStoreList/goodStoreList/batchUpdateExposureStatus',
      },
      goodTypeTree: [],
      goodTypeParam: {},
      storeInfo: {
        key: '',
      },
      userRole:'', // 角色编码，多个逗号隔开
      // 曝光排序相关数据
      exposureSortVisible: false,
      exposureSortLoading: false,
      exposureSortForm: {
        id: '',
        goodName: '',
        currentSortValue: null,
        exposureSortValue: null
      }
    }
  },
  computed: {
    importExcelUrl: function () {
      return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`
    },
  },
  created() {
    if (!this.disableMixinCreated) {
      console.log(' -- mixin created -- ')
      //初始化字典配置 在自己页面定义
      this.initDictConfig()

      // 初始化url路径参数，用于判断是从哪个页面打开的
      let pathQueryParam = getUrlParams()
      //初始化登录人角色编码
      let user = Vue.ls.get(USER_INFO)
      this.userRole = user.userRole
      console.log('this.userRole------>',this.userRole)
      if (this.userRole === 'Merchant') {
        console.log('this.getStoreInfoBySysUserId------>',user.id)
        this.getStoreInfoBySysUserId(user.id)
      }
    }
  },
  methods: {
    getStoreInfoBySysUserId(sysUserId){
      getAction(this.url.getBySysUserId, {
        sysUserId:sysUserId
      }).then((res) => {
        if (res.success) {
          console.log(res.result);
          this.storeInfo.key = res.result.id;
          this.storeInfo.title = res.result.storeName;
          this.getStoreGoodTypeByTree()
        } else {
          this.$message.warning(res.message);
        }
      });
    },
    //启用
    updateStatus: function (id, status) {
      //this.stopRemark="";
      if (!this.url.updateStatus) {
        this.$message.error('请设置url.updateFrameStatus属性!')
        return
      }
      var that = this
      this.$confirm({
        title: '启用商品后，该商品用户将可以搜索、浏览、购买，供应商可以上下架',
        content: '您确定要启用吗？',
        onOk: function () {
          getAction(that.url.updateStatus, { ids: id, statusExplain: '', status: status }).then((res) => {
            if (res.success) {
              that.$message.success(res.message)
              that.loadData()
              that.onClearSelected()
            } else {
              that.$message.warning(res.message)
            }
          })
        },
      })
    },
    //上架
    updateFrameStatus: function (id, FrameStatus) {
      if (!this.url.updateFrameStatus) {
        this.$message.error('请设置url.updateFrameStatus属性!')
        return
      }
      var that = this
      this.$confirm({
        title: '上架商品后，该商品用户将可以搜索、浏览、购买',
        content: '您确定要上架吗？',
        onOk: function () {
          getAction(that.url.updateFrameStatus, {
            ids: id,
            frameStatus: FrameStatus,
            frameExplain: '',
          }).then((res) => {
            if (res.success) {
              that.$message.success(res.message)
              that.loadData()
              that.onClearSelected()
            } else {
              that.$message.warning(res.message)
            }
          })
        },
      })
    },
    //单个曝光状态设置
    updateExposureStatus: function (id, exposureStatus) {
      if (!this.url.batchUpdateExposureStatus) {
        this.$message.error('请设置url.batchUpdateExposureStatus属性!')
        return
      }
      var that = this
      const title = exposureStatus === '1' ? '设置曝光商品' : '取消曝光商品'
      const content = exposureStatus === '1' ?
        '设置为曝光商品后，该商品将在曝光商品列表中显示，员工可通过分享获得业绩' :
        '取消曝光后，该商品将不再在曝光商品列表中显示'

      this.$confirm({
        title: title,
        content: content,
        onOk: function () {
          postApplicationAction(that.url.batchUpdateExposureStatus, {
            ids: id,
            isExposureGood: exposureStatus,
          }).then((res) => {
            if (res.success) {
              that.$message.success(res.message)
              that.loadData()
              that.onClearSelected()
            } else {
              that.$message.warning(res.message)
            }
          })
        },
      })
    },
    //下架
    showModalFrameStatus(id) {
      this.$refs.modalForm1.showModalFrameStatus(id)
      this.$refs.modalForm1.title = '下架'
      this.$refs.modalForm1.disableSubmit = false
    },
    BatchShelves: function (FrameStatus) {
      if (!this.url.updateFrameStatus) {
        this.$message.error('请设置url.updateFrameStatus属性!')
        return
      }
      if (this.selectedRowKeys.length <= 0) {
        this.$message.warning('请选择一条记录！')
        return
      } else {
        var ids = ''
        for (var a = 0; a < this.selectedRowKeys.length; a++) {
          ids += this.selectedRowKeys[a] + ','
        }
        var that = this
        this.$confirm({
          title: '确认修改',
          content: '是否修改选中数据?',
          onOk: function () {
            getAction(that.url.updateFrameStatus, { ids: ids, frameStatus: FrameStatus, frameExplain: '' }).then(
              (res) => {
                if (res.success) {
                  that.$message.success(res.message)
                  that.loadData()
                  that.onClearSelected()
                } else {
                  that.$message.warning(res.message)
                }
              }
            )
          },
        })
      }
    },
    //批量曝光状态设置
    batchUpdateExposureStatus: function (exposureStatus) {
      if (!this.url.batchUpdateExposureStatus) {
        this.$message.error('请设置url.batchUpdateExposureStatus属性!')
        return
      }
      if (this.selectedRowKeys.length <= 0) {
        this.$message.warning('请选择一条记录！')
        return
      }

      var ids = ''
      for (var a = 0; a < this.selectedRowKeys.length; a++) {
        ids += this.selectedRowKeys[a] + ','
      }

      var that = this
      const title = exposureStatus === '1' ? '批量设置曝光商品' : '批量取消曝光商品'
      const content = `是否${exposureStatus === '1' ? '设置' : '取消'}选中的${this.selectedRowKeys.length}个商品为曝光商品?`

      this.$confirm({
        title: title,
        content: content,
        onOk: function () {
          postApplicationAction(that.url.batchUpdateExposureStatus, {
            ids: ids,
            isExposureGood: exposureStatus
          }).then((res) => {
            if (res.success) {
              that.$message.success(res.message)
              that.loadData()
              that.onClearSelected()
            } else {
              that.$message.warning(res.message)
            }
          })
        },
      })
    },
    //批量下架
    showModalFrameStatus1() {
      if (!this.url.updateFrameStatus) {
        this.$message.error('请设置url.updateFrameStatus属性!')
        return
      }
      if (this.selectedRowKeys.length <= 0) {
        this.$message.warning('请选择一条记录！')
        return
      } else {
        var ids = ''
        for (var a = 0; a < this.selectedRowKeys.length; a++) {
          ids += this.selectedRowKeys[a] + ','
        }
      }
      this.$refs.modalForm1.showModalFrameStatus(ids)
      this.$refs.modalForm1.title = '下架'
      this.$refs.modalForm1.disableSubmit = false
    },
    //排序
    changeSort(record) {
      this.$refs.modalForm1.showModalSort(record)
      this.$refs.modalForm1.title = '排序'
      this.$refs.modalForm1.disableSubmit = false
    },
    getSelectStore(e) {
      this.storeInfo = e

      // 如果选择的是"全部店铺"，清空商品分类树并重置URL
      if (e.key === 'ALL_STORES') {
        this.goodTypeTree = []
        this.url.list = '/goodStoreList/goodStoreList/list'
        this.modalFormOk() // 刷新商品列表
      } else {
        // 选择具体店铺时，立即显示该店铺的所有商品
        this.goodTypeTree = []
        // 构建带有店铺过滤参数的URL
        this.url.list = `/goodStoreList/goodStoreList/list?sysUserId=${e.sysUserId}`
        this.modalFormOk() // 刷新商品列表

        // 同时加载该店铺的商品分类树（保持分类筛选功能）
        this.getStoreGoodTypeByTree()
      }
    },
    handleAdd() {
      // if (!this.storeInfo.key) {
      //   this.$message.warning('请先选择左侧店铺信息')
      //   return
      // }
      // this.$refs.modalForm.add(this.goodTypeParam, this.storeInfo)
      //路由跳转
      localStorage.removeItem('GoodListListInfo')
      this.$router.push({ path: '/good/modules/GoodStoreListadd',query:{reusePage:3}})
    },
    handleEdit: function(record) {
      // this.$refs.modalForm.title = '编辑'
      // this.$refs.modalForm.disableSubmit = false
      // this.$refs.modalForm.edit(record)
      //路由跳转
      localStorage.setItem('GoodListListInfo',JSON.stringify(record))
      this.$router.push({ path: '/good/modules/GoodListadd2' ,query:{reusePage:3}})
    },
    getByGoodType(id, level) {
      console.log(id, '----', level)
      this.url.list = '/goodStoreList/goodStoreList/listNew?typeId=' + id + '&level=' + level
      ;(this.goodTypeParam = {
        id: id,
        level: level,
      }),
        this.modalFormOk()
    },
    getStoreGoodTypeByTree() {
      let param = {
        storeManageId: this.storeInfo.key
      }
      getAction(this.url.getStoreGoodTypeByTree,param ).then((res) => {
        if (res.success) {
          this.goodTypeTree = res.result
          console.log(this.goodTypeTree)
        } else {
          this.$message.warning(res.message)
        }
      })
    },
    getImg(text) {
      return window._CONFIG['imgDomainURL'] + '/' + JSON.parse(text)[0]
    },

    // 曝光排序相关方法
    handleExposureSort(record) {
      this.exposureSortForm = {
        id: record.id,
        goodName: record.goodName,
        currentSortValue: record.exposureSortValue || 999,
        exposureSortValue: record.exposureSortValue || 999
      }
      this.exposureSortVisible = true
    },

    handleExposureSortOk() {
      if (!this.exposureSortForm.exposureSortValue) {
        this.$message.error('请输入排序值')
        return
      }

      this.exposureSortLoading = true

      const params = {
        id: this.exposureSortForm.id,
        exposureSortValue: this.exposureSortForm.exposureSortValue
      }

      putAction('/goodStoreList/goodStoreList/updateExposureSortValue', params).then((res) => {
        this.exposureSortLoading = false
        if (res.success) {
          this.$message.success('排序值设置成功')
          this.exposureSortVisible = false
          this.modalFormOk() // 刷新列表
        } else {
          this.$message.error(res.message || '设置失败')
        }
      }).catch((error) => {
        this.exposureSortLoading = false
        this.$message.error('设置失败：' + error.message)
      })
    },

    handleExposureSortCancel() {
      this.exposureSortVisible = false
      this.exposureSortForm = {
        id: '',
        goodName: '',
        currentSortValue: null,
        exposureSortValue: null
      }
    },

    // 处理表格排序、筛选、分页变化
    handleTableChange(pagination, filters, sorter) {
      console.log('表格变化:', { pagination, filters, sorter })

      // 处理排序
      if (sorter && sorter.field) {
        const { field, order } = sorter

        // 根据JeecgBoot规范设置排序参数
        if (order === 'ascend') {
          this.queryParam.column = field
          this.queryParam.order = 'asc'
        } else if (order === 'descend') {
          this.queryParam.column = field
          this.queryParam.order = 'desc'
        }
      } else {
        // 清除排序
        delete this.queryParam.column
        delete this.queryParam.order
      }

      // 重新加载数据
      this.loadData(1)
    },
  },
}
</script>
<style scoped>
@import '~@assets/less/common.less';
</style>