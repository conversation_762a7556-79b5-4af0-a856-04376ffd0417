-- 为店铺商品表添加曝光排序值字段
-- 用于控制曝光商品的显示优先级，数值越小优先级越高

ALTER TABLE `good_store_list` 
ADD COLUMN `exposure_sort_value` INT(11) DEFAULT 999 COMMENT '曝光排序值，数值越小优先级越高，默认999';

-- 为已存在的曝光商品设置默认排序值
UPDATE `good_store_list` 
SET `exposure_sort_value` = 999 
WHERE `exposure_sort_value` IS NULL;

-- 添加索引以优化排序查询性能
CREATE INDEX `idx_exposure_sort_value` ON `good_store_list` (`exposure_sort_value`);

-- 添加复合索引，用于曝光商品的复合排序（佣金比例 + 排序值）
CREATE INDEX `idx_exposure_sort_composite` ON `good_store_list` (`share_commission_rate` DESC, `exposure_sort_value` ASC);
