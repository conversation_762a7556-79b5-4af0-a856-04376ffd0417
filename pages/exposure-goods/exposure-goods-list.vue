<template>
  <view class="exposure-goods-container">

    <!-- 功能按钮区域 -->
    <view class="function-buttons">
      <view class="button-item primary-button" @click="goToPerformanceRanking">
        <uv-icon name="list" :size="iconSize" color="#ffffff"></uv-icon>
        <text class="button-text">业绩排行榜</text>
      </view>
      <view class="button-item secondary-button" @click="goToMonthlyRanking">
        <uv-icon name="calendar" :size="iconSize" color="#667eea"></uv-icon>
        <text class="button-text">月排行榜</text>
      </view>
    </view>

    <!-- 底部固定申请按钮 -->
    <view class="bottom-apply-button" v-if="!isRanker">
      <uv-button
        type="primary"
        text="申请成为打榜者"
        color="#667eea"
        @click="goToRankerApplication"
        :loading="applying"
      ></uv-button>
    </view>

    <!-- 商品列表区域 -->
    <view class="goods-list">
      <!-- 下拉刷新 -->
      <scroll-view 
        scroll-y 
        class="scroll-view"
        @scrolltolower="loadMore"
        refresher-enabled
        @refresherrefresh="onRefresh"
        :refresher-triggered="refreshing"
      >
        <!-- 商品卡片 -->
        <view 
          class="exposure-good-card" 
          v-for="(item, index) in goodsList" 
          :key="item.id"
          @click="goToGoodDetail(item)"
        >
          <!-- 左侧商品图片 -->
          <view class="good-image">
            <uv-image 
              :src="getGoodImage(item.mainPicture)" 
              width="80px" 
              height="80px" 
              radius="8px"
              :fade="true"
              :loading="true"
              error-icon="photo"
            ></uv-image>
          </view>

          <!-- 右侧商品信息 -->
          <view class="good-info">
            <!-- 商品名称 -->
            <view class="good-name">{{ item.goodName }}</view>
            
            <!-- 价格和库存 -->
            <view class="price-stock-row">
              <text class="price">{{ item.smallPrice }}</text>
              <text class="stock" :class="getStockClass(item.stock)">
                📦 库存 {{ item.stock || 0 }}
              </text>
            </view>
            
            <!-- 佣金比例 -->
            <view class="commission-row">
              <text class="commission">佣金比例：{{ item.shareCommissionRate }}%</text>
            </view>
          </view>
        </view>

        <!-- 空状态 -->
        <view class="empty-state" v-if="!loading && goodsList.length === 0">
          <uv-empty 
            mode="data" 
            icon="https://cdn.uviewui.com/uview/empty/data.png"
            text="暂无曝光商品"
            textColor="#999999"
            textSize="14"
          >
            <template #bottom>
              <text class="empty-desc">管理员还未设置曝光商品</text>
            </template>
          </uv-empty>
        </view>

        <!-- 加载更多 -->
        <uv-load-more 
          :status="loadStatus" 
          :loading-text="loadingText"
          :loadmore-text="loadmoreText"
          :nomore-text="nomoreText"
          v-if="goodsList.length > 0"
        ></uv-load-more>
      </scroll-view>
    </view>
  </view>
</template>

<script>
import { userStore } from '@/store'
import { parseImgurl } from '@/utils'

export default {
  name: 'ExposureGoodsList',
  data() {
    return {
      goodsList: [],
      loading: false,
      refreshing: false,
      pageNo: 1,
      pageSize: 10,
      hasMore: true,
      loadStatus: 'loadmore', // loadmore, loading, nomore
      loadingText: '加载中...',
      loadmoreText: '点击加载更多',
      nomoreText: '没有更多了',
      applying: false // 申请按钮加载状态
    }
  },
  computed: {
    // 判断当前用户是否为打榜者
    isRanker() {
      return userStore().userInfo?.isRanker === '1'
    },

    // 兼容性：保留员工判断（向后兼容）
    isEmployee() {
      return userStore().userInfo?.isEmployee === '1'
    },

    // 响应式图标尺寸
    iconSize() {
      const systemInfo = uni.getSystemInfoSync()
      const screenWidth = systemInfo.screenWidth

      if (screenWidth <= 320) {
        return 14
      } else if (screenWidth <= 375) {
        return 15
      } else if (screenWidth >= 768) {
        return 18
      } else {
        return 16
      }
    }
  },
  onLoad() {
    this.loadGoodsList()
  },
  onShow() {
    // 页面显示时刷新数据
    this.refreshData()
  },
  methods: {
    // 跳转到打榜者业绩排行榜
    goToPerformanceRanking() {
      uni.navigateTo({
        url: '/pages/exposure-goods/performance-ranking'
      })
    },

    // 跳转到月度排行榜
    goToMonthlyRanking() {
      uni.navigateTo({
        url: '/pages/exposure-goods/monthly-ranking'
      })
    },

    // 跳转到打榜者申请页面
    goToRankerApplication() {
      uni.navigateTo({
        url: '/pages/exposure-goods/ranker-application'
      })
    },

    // 兼容性：保留员工申请方法
    goToEmployeeApplication() {
      this.goToRankerApplication()
    },

    // 获取商品图片URL - 完全按照店铺首页GoodsItem.vue的正确实现
    getGoodImage(mainPicture) {
      if (!mainPicture) return ''
      const imageUrl = parseImgurl(mainPicture)?.[0]
      return imageUrl ? uni.env.IMAGE_URL + imageUrl : ''
    },

    // 获取库存状态样式类
    getStockClass(stock) {
      const stockNum = Number(stock) || 0
      if (stockNum === 0) {
        return 'no-stock'
      } else if (stockNum < 10) {
        return 'low-stock'
      }
      return 'normal-stock'
    },

    // 跳转到商品详情页（携带曝光来源参数）
    goToGoodDetail(good) {
      // 构建完整的参数对象，确保包含后端必需的isPlatform参数
      const params = {
        id: good.id,
        goodId: good.id, // 兼容性参数
        isPlatform: good.isPlatform !== undefined ? good.isPlatform : 0, // 默认为店铺商品
        isTransition: '1', // 后端兼容性要求
        fromExposureList: 'true', // 曝光来源标识
        exposureGoodId: good.id, // 曝光商品ID，用于业绩统计
        // 添加可能的营销活动参数（参考goodResultId函数）
        ...(good.marketingPrefectureId && { marketingPrefectureId: good.marketingPrefectureId }),
        ...(good.marketingFreeGoodListId && { marketingFreeGoodListId: good.marketingFreeGoodListId }),
        ...(good.marketingStorePrefectureGoodId && { marketingStorePrefectureGoodId: good.marketingStorePrefectureGoodId }),
        ...(good.marketingZoneGroupGoodId && { marketingZoneGroupGoodId: good.marketingZoneGroupGoodId }),
        ...(good.marketingZoneGroupId && { marketingZoneGroupId: good.marketingZoneGroupId }),
        ...(good.marketingRushGoodId && { marketingRushGoodId: good.marketingRushGoodId }),
        ...(good.marketingStoreGiftCardMemberListId && { marketingStoreGiftCardMemberListId: good.marketingStoreGiftCardMemberListId }),
        ...(good.tMemberId && { tMemberId: good.tMemberId })
      }

      // 构建URL参数字符串
      const paramStr = Object.keys(params).map(key => `${key}=${params[key]}`).join('&')
      const url = `/packageGoods/pages/productInfo/productInfo?${paramStr}`

      console.log('🎯 曝光商品跳转详情页，参数:', params)
      uni.navigateTo({ url })
    },

    // 加载曝光商品列表
    async loadGoodsList(isRefresh = false) {
      if (this.loading) return
      
      if (isRefresh) {
        this.pageNo = 1
        this.hasMore = true
        this.goodsList = []
      }
      
      if (!this.hasMore) return
      
      this.loading = true
      this.loadStatus = 'loading'
      
      try {
        const params = {
          pageNo: this.pageNo,
          pageSize: this.pageSize,
          memberId: userStore().userInfo?.id || ''
        }
        
        const { data } = await uni.http.get(uni.api.getExposureGoodsList, { params })
        
        if (data.success) {
          const result = data.result
          const newList = result.records || []

          if (isRefresh) {
            this.goodsList = newList
          } else {
            this.goodsList.push(...newList)
          }

          // 判断是否还有更多数据
          this.hasMore = this.goodsList.length < result.total
          this.pageNo++

          this.loadStatus = this.hasMore ? 'loadmore' : 'nomore'
        }
      } catch (error) {
        console.error('获取曝光商品列表失败:', error)
        // 错误处理已在拦截器中统一处理
      } finally {
        this.loading = false
        this.refreshing = false
      }
    },

    // 下拉刷新
    onRefresh() {
      this.refreshing = true
      this.loadGoodsList(true)
    },

    // 上拉加载更多
    loadMore() {
      if (this.hasMore && !this.loading) {
        this.loadGoodsList()
      }
    },

    // 刷新数据
    refreshData() {
      this.loadGoodsList(true)
    }
  }
}
</script>

<style lang="scss" scoped>
.exposure-goods-container {
  min-height: 100vh;
  background-color: #f5f5f5;
}

// 功能按钮区域
.function-buttons {
  display: flex;
  padding: 12px 16px;
  background-color: #ffffff;
  margin-bottom: 8px;
  gap: 16px;

  // 响应式适配
  @media screen and (max-width: 375px) {
    padding: 10px 12px;
    gap: 12px;
  }

  @media screen and (min-width: 768px) {
    padding: 16px 24px;
    gap: 20px;
  }

  .button-item {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 12px 16px;
    border-radius: 12px;
    gap: 8px;
    min-height: 44px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;

    .button-text {
      font-size: 14px;
      font-weight: 500;
      transition: all 0.3s ease;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    // 小屏幕适配
    @media screen and (max-width: 375px) {
      padding: 10px 12px;
      min-height: 40px;
      gap: 6px;

      .button-text {
        font-size: 13px;
      }
    }

    // 大屏幕适配
    @media screen and (min-width: 768px) {
      padding: 14px 20px;
      min-height: 48px;
      gap: 10px;

      .button-text {
        font-size: 15px;
      }
    }

    // 超小屏幕特殊处理
    @media screen and (max-width: 320px) {
      padding: 8px 10px;
      min-height: 36px;

      .button-text {
        font-size: 12px;
      }
    }
  }

  // 主按钮样式 - 员工业绩排行榜
  .primary-button {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);

    .button-text {
      color: #ffffff;
    }

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 16px rgba(102, 126, 234, 0.4);
    }

    &:active {
      transform: translateY(0px);
      box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
    }
  }

  // 次按钮样式 - 申请成为员工
  .secondary-button {
    border: 1px solid #667eea;
    background-color: #ffffff;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1);

    .button-text {
      color: #667eea;
    }

    &:hover {
      background-color: #f8f9ff;
      border-color: #5a6fd8;
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(102, 126, 234, 0.2);
    }

    &:active {
      background-color: #f0f4ff;
      border-color: #4c63d2;
      transform: translateY(0px);
    }
  }
}

// 商品列表区域
.goods-list {
  flex: 1;

  .scroll-view {
    height: calc(100vh - 200px);
  }
}

// 曝光商品卡片 - 符合现代卡片式设计规范
.exposure-good-card {
  display: flex;
  background: #ffffff;
  border-radius: 16px; // 16px卡片圆角
  margin: 8px 16px;
  padding: 16px;
  box-shadow: 0 2px 12px rgba(102, 126, 234, 0.08);
  gap: 12px;
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.98);
    box-shadow: 0 1px 6px rgba(102, 126, 234, 0.12);
  }

  .good-image {
    flex-shrink: 0;
    border-radius: 12px; // 12px组件圆角
    overflow: hidden;
  }

  .good-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 4px;

    .good-name {
      font-size: 16px;
      font-weight: 600;
      color: #1a1a1a;
      line-height: 1.4;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
      overflow: hidden;
      margin-bottom: 8px;
    }

    .price-stock-row {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 8px;

      .price {
        font-size: 18px;
        font-weight: 700;
        color: #ff4757;

        &::before {
          content: "助力值 ";
          font-size: 12px;
          color: #666;
          margin-right: 2px;
        }
      }

      .stock {
        font-size: 12px;
        font-weight: 600;
        padding: 2px 8px;
        border-radius: 6px;

        &.normal-stock {
          background: rgba(34, 163, 255, 0.1);
          color: #22A3FF;
        }

        &.low-stock {
          background: rgba(255, 143, 0, 0.1);
          color: #FF8F00;

          &::before {
            content: "⚠️ ";
          }
        }

        &.no-stock {
          background: rgba(255, 71, 87, 0.1);
          color: #FF4757;

          &::before {
            content: "❌ ";
          }
        }
      }
    }

    .commission-row {
      .commission {
        font-size: 13px;
        color: #667eea; // 品牌色
        font-weight: 600;
        background: rgba(102, 126, 234, 0.1);
        padding: 4px 8px;
        border-radius: 12px; // 12px组件圆角
        display: inline-block;
      }
    }
  }
}

// 底部固定申请按钮
.bottom-apply-button {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 16px 20px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-top: 1px solid rgba(0, 0, 0, 0.05);
  z-index: 999;

  // 安全区域适配
  padding-bottom: calc(16px + env(safe-area-inset-bottom));
}

// 空状态
.empty-state {
  padding: 60px 20px;
  text-align: center;

  .empty-desc {
    font-size: 12px;
    color: #999999;
    margin-top: 8px;
  }
}
</style>
